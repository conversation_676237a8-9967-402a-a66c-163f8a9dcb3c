// Copyright 2023 The gVisor Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package erofs

import "unsafe"

// pointerAt returns a pointer to offset off within the memory backed by image.
//
// Precondition: Callers are responsible for the range check.
func (i *Image) pointerAt(off uint64) unsafe.Pointer {
	// Although callers will always do the range check, there is no need to
	// bother with the slice's builtin range check below. Because this function
	// will be inlined into callers, and there are no redundant checks and
	// unnecessary out-of-range panic calls in the code generated by the compiler.
	return unsafe.Pointer(&i.bytes[off])
}

// direntAfter returns a pointer to the next adjacent dirent after dirent d.
//
// Preconditions:
//   - d is a pointer to the memory backed by image.
//   - d is not the last dirent in its block.
func direntAfter(d *Dirent) *Dirent {
	return (*Dirent)(unsafe.Pointer(uintptr(unsafe.Pointer(d)) + DirentSize))
}
