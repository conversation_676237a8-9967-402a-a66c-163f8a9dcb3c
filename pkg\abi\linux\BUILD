load("//tools:defs.bzl", "go_library", "go_test")

# Package linux contains the constants and types needed to interface with a
# Linux kernel. It should be used instead of syscall or golang.org/x/sys/unix
# when the host OS may not be Linux.

package(
    default_applicable_licenses = ["//:license"],
    licenses = ["notice"],
)

go_library(
    name = "linux",
    srcs = [
        "aio.go",
        "arch_amd64.go",
        "audit.go",
        "bpf.go",
        "capability.go",
        "clone.go",
        "context.go",
        "dev.go",
        "elf.go",
        "epoll.go",
        "epoll_amd64.go",
        "epoll_arm64.go",
        "errqueue.go",
        "eventfd.go",
        "exec.go",
        "fadvise.go",
        "fcntl.go",
        "file.go",
        "file_amd64.go",
        "file_arm64.go",
        "fs.go",
        "fuse.go",
        "futex.go",
        "inotify.go",
        "ioctl.go",
        "ioctl_tun.go",
        "iouring.go",
        "ip.go",
        "ipc.go",
        "keyctl.go",
        "limits.go",
        "linux.go",
        "membarrier.go",
        "mm.go",
        "mm_amd64.go",
        "mm_arm64.go",
        "mqueue.go",
        "msgqueue.go",
        "netdevice.go",
        "netfilter.go",
        "netfilter_bridge.go",
        "netfilter_ipv4.go",
        "netfilter_ipv6.go",
        "netlink.go",
        "netlink_netfilter.go",
        "netlink_route.go",
        "nf_tables.go",
        "poll.go",
        "prctl.go",
        "ptrace.go",
        "ptrace_amd64.go",
        "ptrace_arm64.go",
        "rseq.go",
        "rusage.go",
        "sched.go",
        "seccomp.go",
        "sem.go",
        "sem_amd64.go",
        "sem_arm64.go",
        "shm.go",
        "signal.go",
        "signalfd.go",
        "socket.go",
        "splice.go",
        "tcp.go",
        "time.go",
        "timer.go",
        "tty.go",
        "uio.go",
        "utsname.go",
        "vfio.go",
        "vfio_unsafe.go",
        "wait.go",
        "xattr.go",
    ],
    marshal = True,
    visibility = ["//visibility:public"],
    deps = [
        "//pkg/abi",
        "//pkg/bits",
        "//pkg/context",
        "//pkg/hostarch",
        "//pkg/marshal",
        "//pkg/marshal/primitive",
        "@org_golang_x_sys//unix:go_default_library",
    ],
)

go_test(
    name = "linux_test",
    size = "small",
    srcs = [
        "netfilter_test.go",
    ],
    library = ":linux",
)
