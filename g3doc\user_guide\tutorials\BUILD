load("//website:defs.bzl", "doc")

package(
    default_applicable_licenses = ["//:license"],
    default_visibility = ["//website:__pkg__"],
    licenses = ["notice"],
)

doc(
    name = "docker",
    src = "docker.md",
    category = "User Guide",
    permalink = "/docs/tutorials/docker/",
    subcategory = "Tutorials",
    weight = "10",
)

doc(
    name = "docker_compose",
    src = "docker-compose.md",
    category = "User Guide",
    permalink = "/docs/tutorials/docker-compose/",
    subcategory = "Tutorials",
    weight = "20",
)

doc(
    name = "kubernetes",
    src = "kubernetes.md",
    category = "User Guide",
    data = [
        "add-node-pool.png",
        "node-pool-button.png",
    ],
    permalink = "/docs/tutorials/kubernetes/",
    subcategory = "Tutorials",
    weight = "30",
)

doc(
    name = "falco",
    src = "falco.md",
    category = "User Guide",
    permalink = "/docs/tutorials/falco/",
    subcategory = "Tutorials",
    weight = "35",
)

doc(
    name = "knative",
    src = "knative.md",
    category = "User Guide",
    permalink = "/docs/tutorials/knative/",
    subcategory = "Tutorials",
    weight = "40",
)

doc(
    name = "cni",
    src = "cni.md",
    category = "User Guide",
    permalink = "/docs/tutorials/cni/",
    subcategory = "Tutorials",
    weight = "50",
)

doc(
    name = "docker_in_gvisor",
    src = "docker-in-gvisor.md",
    category = "User Guide",
    permalink = "/docs/tutorials/docker-in-gvisor/",
    subcategory = "Tutorials",
    weight = "50",
)

doc(
    name = "docker_in_gke_sandbox",
    src = "docker-in-gke-sandbox.md",
    category = "User Guide",
    permalink = "/docs/tutorials/docker-in-gke-sandbox/",
    subcategory = "Tutorials",
    weight = "50",
)
