// Copyright 2020 The gVisor Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Package goid provides the Get function.
package goid

import (
	_ "runtime" // For facts in assembly files.
)

// goid returns the current goid, it is defined in assembly.
func goid() int64

// Get returns the ID of the current goroutine.
func Get() int64 {
	return goid()
}
