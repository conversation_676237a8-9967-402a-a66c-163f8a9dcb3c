name: Mark stale issues and pull requests

# Please refer to https://github.com/actions/stale/blob/master/action.yml
# to see all config knobs of the stale action.

"on":
  schedule:
  - cron: "0 0 * * *"

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/stale@v9
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'A friendly reminder that this issue had no activity for 120 days.'
        stale-pr-message: 'A friendly reminder that this PR had no activity for 120 days.'
        stale-issue-label: 'stale-issue'
        stale-pr-label: 'stale-pr'
        days-before-stale: 120
        days-before-close: 90
        remove-stale-when-updated: true
        exempt-pr-labels: 'no-auto-close'
        exempt-issue-labels: 'no-auto-close,revived,type: enhancement,good first issue,status: help wanted,type: proposal'
        operations-per-run: 1000
        close-issue-label: 'auto-closed'
        close-pr-label: 'auto-closed'
        close-issue-message: 'This issue has been closed due to lack of activity.'
        close-pr-message: 'This PR has been closed due to lack of activity.'