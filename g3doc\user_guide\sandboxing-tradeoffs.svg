<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   width="426.70343mm"
   height="630.70361mm"
   viewBox="0 0 426.70343 630.70361"
   version="1.1"
   id="svg5"
   sodipodi:docname="cluster-architecture.svg"
   inkscape:version="1.1.2 (0a00cf5339, 2022-02-04)"
   inkscape:export-filename="/home/<USER>/cluster-architecture.png"
   inkscape:export-xdpi="44.092609"
   inkscape:export-ydpi="44.092609"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <sodipodi:namedview
     id="namedview7"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:document-units="mm"
     showgrid="false"
     inkscape:snap-global="true"
     inkscape:object-paths="false"
     inkscape:snap-smooth-nodes="true"
     inkscape:snap-midpoints="true"
     inkscape:snap-object-midpoints="true"
     inkscape:zoom="0.57370932"
     inkscape:cx="785.24087"
     inkscape:cy="2265.9559"
     inkscape:window-width="1920"
     inkscape:window-height="999"
     inkscape:window-x="0"
     inkscape:window-y="52"
     inkscape:window-maximized="1"
     inkscape:current-layer="layer1"
     width="710mm"
     height="600mm"
     fit-margin-top="0.2"
     fit-margin-left="0.2"
     fit-margin-right="0.2"
     fit-margin-bottom="0.2" />
  <defs
     id="defs2">
    <rect
       x="8.7035122"
       y="2243.2947"
       width="1603.3627"
       height="158.52296"
       id="rect175836" />
    <rect
       x="18.624516"
       y="12.215158"
       width="799.76781"
       height="83.872802"
       id="rect124985" />
    <rect
       x="64.782188"
       y="880.97192"
       width="227.32933"
       height="121.63048"
       id="rect112375" />
    <marker
       id="ExperimentalArrow"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path80303" />
    </marker>
    <marker
       style="overflow:visible"
       id="TriangleOutL"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="TriangleOutL"
       inkscape:isstock="true">
      <path
         transform="scale(0.8)"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:1pt"
         d="M 5.77,0 -2.88,5 V -5 Z"
         id="path80192" />
    </marker>
    <marker
       style="overflow:visible"
       id="Tail"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Tail"
       inkscape:isstock="true">
      <g
         transform="scale(-1.2)"
         id="g80098">
        <path
           style="fill:none;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.8;stroke-linecap:round"
           d="M -3.8048674,-3.9585227 0.54352094,0"
           id="path80086" />
        <path
           style="fill:none;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.8;stroke-linecap:round"
           d="M -1.2866832,-3.9585227 3.0617053,0"
           id="path80088" />
        <path
           style="fill:none;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.8;stroke-linecap:round"
           d="M 1.3053582,-3.9585227 5.6537466,0"
           id="path80090" />
        <path
           style="fill:none;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.8;stroke-linecap:round"
           d="M -3.8048674,4.1775838 0.54352094,0.21974226"
           id="path80092" />
        <path
           style="fill:none;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.8;stroke-linecap:round"
           d="M -1.2866832,4.1775838 3.0617053,0.21974226"
           id="path80094" />
        <path
           style="fill:none;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.8;stroke-linecap:round"
           d="M 1.3053582,4.1775838 5.6537466,0.21974226"
           id="path80096" />
      </g>
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Mstart"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Mstart"
       inkscape:isstock="true">
      <path
         transform="scale(0.6)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80074" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow1Lend"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow1Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-0.8,0,0,-0.8,-10,0)"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:1pt"
         d="M 0,0 5,-5 -12.5,0 5,5 Z"
         id="path80053" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow1Lstart"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow1Lstart"
       inkscape:isstock="true">
      <path
         transform="matrix(0.8,0,0,0.8,10,0)"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:1pt"
         d="M 0,0 5,-5 -12.5,0 5,5 Z"
         id="path80050" />
    </marker>
    <rect
       x="33.444901"
       y="943.00214"
       width="734.0896"
       height="114.22172"
       id="rect62484" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682" />
    <rect
       x="40.032753"
       y="318.92899"
       width="481.13391"
       height="49.494644"
       id="rect4918" />
    <inkscape:perspective
       sodipodi:type="inkscape:persp3d"
       inkscape:vp_x="-16.570846 : 501.35882 : 1"
       inkscape:vp_y="0 : 999.99995 : 0"
       inkscape:vp_z="118.57937 : 609.66021 : 1"
       inkscape:persp3d-origin="105 : 432.7036 : 1"
       id="perspective37" />
    <rect
       x="40.032753"
       y="318.92899"
       width="395.95694"
       height="49.494644"
       id="rect4918-7" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-3" />
    <rect
       x="40.032753"
       y="318.92899"
       width="469.62326"
       height="48.343536"
       id="rect4918-7-9" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-3-0" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-36" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-2" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-2" />
    <marker
       style="overflow:visible"
       id="Arrow2Lend-3"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-7" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-1"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-9" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-5"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-0" />
    </marker>
    <rect
       x="126.88659"
       y="465.03427"
       width="263.58771"
       height="112.8016"
       id="rect22682-6" />
    <rect
       x="40.032753"
       y="318.92899"
       width="481.13391"
       height="49.494644"
       id="rect4918-8" />
    <rect
       x="40.032753"
       y="318.92899"
       width="395.95694"
       height="49.494644"
       id="rect4918-7-5" />
    <rect
       x="40.032753"
       y="318.92899"
       width="469.62326"
       height="48.343536"
       id="rect4918-7-9-6" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-1" />
    <rect
       x="126.88659"
       y="465.03427"
       width="263.58771"
       height="112.8016"
       id="rect22682-6-1" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-36-5" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-2-9" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-84" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-8" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-2-1" />
    <rect
       x="33.444901"
       y="943.00214"
       width="734.0896"
       height="114.22172"
       id="rect62484-0" />
    <marker
       id="ExperimentalArrow-3"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path80303-0" />
    </marker>
    <marker
       id="marker132232"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path132230" />
    </marker>
    <marker
       id="marker132236"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path132234" />
    </marker>
    <marker
       id="marker132240"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path132238" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-4"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-4" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-4"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-4" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-5-7"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-0-6" />
    </marker>
    <rect
       x="18.624516"
       y="12.215158"
       width="775.59607"
       height="68.909325"
       id="rect124985-3" />
    <rect
       x="40.032753"
       y="318.92899"
       width="481.13391"
       height="49.494644"
       id="rect4918-9" />
    <rect
       x="40.032753"
       y="318.92899"
       width="395.95694"
       height="49.494644"
       id="rect4918-7-7" />
    <rect
       x="40.032753"
       y="318.92899"
       width="469.62326"
       height="48.343536"
       id="rect4918-7-9-7" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-64" />
    <rect
       x="126.88659"
       y="465.03427"
       width="263.58771"
       height="112.8016"
       id="rect22682-6-3" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-36-0" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-2-3" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-0" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-9" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-2-2" />
    <rect
       x="33.444901"
       y="943.00214"
       width="734.0896"
       height="114.22172"
       id="rect62484-5" />
    <marker
       id="ExperimentalArrow-4"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path80303-05" />
    </marker>
    <marker
       id="marker132232-9"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path132230-4" />
    </marker>
    <marker
       id="marker132236-6"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path132234-9" />
    </marker>
    <marker
       id="marker132240-2"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path132238-2" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-47"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-7" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-54"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-8" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-5-1"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-0-2" />
    </marker>
    <rect
       x="18.624516"
       y="12.215158"
       width="775.59607"
       height="68.909325"
       id="rect124985-8" />
    <rect
       x="40.032753"
       y="318.92899"
       width="469.62326"
       height="48.343536"
       id="rect4918-7-9-7-3" />
    <rect
       x="18.624516"
       y="12.215158"
       width="843.5073"
       height="140.62208"
       id="rect124985-0" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-9" />
    <rect
       x="126.88659"
       y="465.03427"
       width="263.58771"
       height="112.8016"
       id="rect22682-6-19" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-36-6" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-2-93" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-3" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-80" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-2-5" />
    <marker
       id="ExperimentalArrow-6"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path80303-6" />
    </marker>
    <marker
       id="marker157641"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path157639" />
    </marker>
    <marker
       id="marker157645"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path157643" />
    </marker>
    <marker
       id="marker157649"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path157647" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-40"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-0" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-46"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-2" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-5-6"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-0-7" />
    </marker>
    <rect
       x="126.88659"
       y="465.03427"
       width="263.58771"
       height="112.8016"
       id="rect22682-6-19-9" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-3-1" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-80-0" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-2-5-7" />
    <marker
       id="ExperimentalArrow-6-5"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path80303-6-8" />
    </marker>
    <marker
       id="marker159007"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path159005" />
    </marker>
    <marker
       id="marker159011"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path159009" />
    </marker>
    <marker
       id="marker159015"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path159013" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-40-7"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-0-0" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-46-4"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-2-8" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-5-6-0"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-0-7-4" />
    </marker>
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-9-2" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-36-6-9" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-2-93-6" />
    <rect
       x="126.88659"
       y="465.03427"
       width="263.58771"
       height="112.8016"
       id="rect22682-6-19-6" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-3-0" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-80-7" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-2-5-0" />
    <marker
       id="ExperimentalArrow-6-1"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path80303-6-0" />
    </marker>
    <marker
       id="marker160735"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path160733" />
    </marker>
    <marker
       id="marker160739"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path160737" />
    </marker>
    <marker
       id="marker160743"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path160741" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-40-1"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-0-3" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-46-7"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-2-7" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-5-6-2"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-0-7-6" />
    </marker>
    <rect
       x="18.624516"
       y="12.215158"
       width="775.59607"
       height="68.909325"
       id="rect124985-0-4" />
    <rect
       x="40.032753"
       y="318.92899"
       width="481.13391"
       height="49.494644"
       id="rect4918-8-5" />
    <rect
       x="40.032753"
       y="318.92899"
       width="395.95694"
       height="49.494644"
       id="rect4918-7-5-2" />
    <rect
       x="33.444901"
       y="943.00214"
       width="734.0896"
       height="114.22172"
       id="rect62484-0-0" />
    <rect
       x="40.032753"
       y="318.92899"
       width="481.13391"
       height="49.494644"
       id="rect4918-9-2" />
    <rect
       x="40.032753"
       y="318.92899"
       width="395.95694"
       height="49.494644"
       id="rect4918-7-7-9" />
    <rect
       x="40.032753"
       y="318.92899"
       width="469.62326"
       height="48.343536"
       id="rect4918-7-9-7-0" />
    <rect
       x="40.032753"
       y="318.92899"
       width="469.62326"
       height="48.343536"
       id="rect4918-7-9-7-3-9" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-64-9" />
    <rect
       x="126.88659"
       y="465.03427"
       width="263.58771"
       height="112.8016"
       id="rect22682-6-3-4" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-36-0-5" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-2-3-1" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-0-0" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-9-3" />
    <rect
       x="141.49544"
       y="729.7063"
       width="234.83281"
       height="82.887421"
       id="rect45099-8-2-2-7" />
    <rect
       x="33.444901"
       y="943.00214"
       width="734.0896"
       height="114.22172"
       id="rect62484-5-8" />
    <marker
       id="ExperimentalArrow-4-8"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path80303-05-6" />
    </marker>
    <marker
       id="marker160771"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path160769" />
    </marker>
    <marker
       id="marker160775"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path160773" />
    </marker>
    <marker
       id="marker160779"
       refX="5"
       refY="3"
       orient="auto-start-reverse"
       inkscape:stockid="ExperimentalArrow"
       inkscape:isstock="true">
      <path
         style="fill:context-stroke"
         d="M 10,3 0,6 V 0 Z"
         id="path160777" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-47-0"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-7-4" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-54-6"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-8-7" />
    </marker>
    <marker
       style="overflow:visible"
       id="Arrow2Lend-8-3-2-7-5-1-6"
       refX="0"
       refY="0"
       orient="auto"
       inkscape:stockid="Arrow2Lend"
       inkscape:isstock="true">
      <path
         transform="matrix(-1.1,0,0,-1.1,-1.1,0)"
         d="M 8.7185878,4.0337352 -2.2072895,0.01601326 8.7185884,-4.0017078 c -1.7454984,2.3720609 -1.7354408,5.6174519 -6e-7,8.035443 z"
         style="fill:context-stroke;fill-rule:evenodd;stroke:context-stroke;stroke-width:0.625;stroke-linejoin:round"
         id="path80071-9-6-9-8-0-2-0" />
    </marker>
    <rect
       x="18.624516"
       y="12.215158"
       width="775.59607"
       height="68.909325"
       id="rect124985-8-9" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-9-7" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-36-6-5" />
    <rect
       x="126.88659"
       y="465.03427"
       width="199.12958"
       height="67.911118"
       id="rect22682-2-93-9" />
    <rect
       x="18.624516"
       y="12.215158"
       width="799.76782"
       height="83.872803"
       id="rect124985-08" />
    <rect
       x="18.624516"
       y="12.215158"
       width="794.01271"
       height="165.59656"
       id="rect124985-08-0" />
  </defs>
  <g
     inkscape:groupmode="layer"
     id="layer2"
     inkscape:label="Stack"
     transform="translate(-4.568093,-6.3832336)">
    <rect
       style="fill:#000000;fill-opacity:0.02;fill-rule:evenodd;stroke:#000000;stroke-width:1.32603;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="rect117"
       width="194.16092"
       height="163.3266"
       x="8.8489628"
       y="86.176056" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,103.28433,150.98706)"
       id="text4916"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="40.033203"
         y="354.32031"
         id="tspan212223">Your cloud stack</tspan></text>
    <rect
       style="fill:#000000;fill-opacity:0.02;fill-rule:evenodd;stroke:#000000;stroke-width:2.48102;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="rect117-1"
       width="363.27798"
       height="305.58649"
       x="954.23346"
       y="-174.431"
       transform="matrix(0.53446929,0,0,0.53446929,-278.93727,180.35567)" />
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer3"
     inkscape:label="K8s"
     transform="translate(-4.568093,-6.3832336)">
    <rect
       style="fill:#2080ff;fill-opacity:0.0866484;fill-rule:evenodd;stroke:#000000;stroke-width:1.08204;stroke-miterlimit:4;stroke-dasharray:1.08204, 2.16408;stroke-dashoffset:0;stroke-opacity:1"
       id="rect117-6"
       width="177.61057"
       height="118.88654"
       x="17.140287"
       y="114.20395" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,80.998342,134.19592)"
       id="text4916-5"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-7);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="40.033203"
         y="354.32031"
         id="tspan212225">Kubernetes Cluster</tspan></text>
    <rect
       style="fill:#2080ff;fill-opacity:0.0866484;fill-rule:evenodd;stroke:#000000;stroke-width:2.18677;stroke-miterlimit:4;stroke-dasharray:2.18677, 4.37355;stroke-dashoffset:0;stroke-opacity:1"
       id="rect117-6-5"
       width="332.31201"
       height="259.52347"
       x="969.74664"
       y="-159.07536"
       transform="matrix(0.53446929,0,0,0.53446929,-278.93727,180.35567)" />
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer4"
     inkscape:label="Containers_box"
     transform="translate(-4.568093,-6.3832336)">
    <rect
       style="fill:#262362;fill-opacity:0.119759;stroke:#000000;stroke-width:1.9401;stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:1.9401, 3.88022;stroke-dashoffset:0;stroke-opacity:1"
       id="rect31353-6"
       width="311.08279"
       height="212.98085"
       x="977.84808"
       y="-147.48265"
       transform="matrix(0.53446929,0,0,0.53446929,-278.93727,180.35567)" />
  </g>
  <g
     inkscape:label="Top"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(-4.568093,-6.3832336)">
    <ellipse
       style="fill:#fe0000;fill-opacity:0.213973;stroke:#000000;stroke-width:1;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path15701"
       cx="76.683975"
       cy="45.857128"
       rx="46.290901"
       ry="15.684086" />
    <ellipse
       style="fill:#ffa700;fill-opacity:0.190454;stroke:#000000;stroke-width:0.859103;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868"
       cx="47.195267"
       cy="135.89984"
       rx="28.393295"
       ry="13.157152" />
    <ellipse
       style="fill:#e6fae0;fill-opacity:1;stroke:#000000;stroke-width:0.803215;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2"
       cx="53.40995"
       cy="197.19583"
       rx="24.766687"
       ry="13.185097" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,-12.7,4.7625005)"
       id="text22680"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="190.22717"
         y="488.62894"
         id="tspan212229"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212227">Load 
</tspan></tspan><tspan
         x="168.47584"
         y="521.96231"
         id="tspan212233"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212231">balancer</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,7.6666315,-89.212035)"
       id="text22680-10"
       style="font-style:normal;font-weight:normal;font-size:42.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-6);fill:#000000;fill-opacity:1;stroke:none"
       x="171.41656"
       y="0"><tspan
         x="176.29519"
         y="502.78518"
         id="tspan212237"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212235">Outside </tspan></tspan><tspan
         x="199.94104"
         y="556.11856"
         id="tspan212241"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212239">world</tspan></tspan></text>
    <ellipse
       style="fill:#39c681;fill-opacity:0.210933;stroke:#000000;stroke-width:0.859103;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-0"
       cx="106.87472"
       cy="135.75227"
       rx="28.393295"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,46.979476,4.6149914)"
       id="text22680-6"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-36);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="163.78182"
         y="488.62894"
         id="tspan212245"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212243">Static file </tspan></tspan><tspan
         x="177.31048"
         y="521.96231"
         id="tspan212249"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212247">serving</tspan></tspan></text>
    <ellipse
       style="fill:#fff8ba;fill-opacity:0.753457;stroke:#000000;stroke-width:0.859103;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-6"
       cx="165.61224"
       cy="136.28143"
       rx="28.393295"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,105.71698,8.848325)"
       id="text22680-1"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-2);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="158.59301"
         y="488.62894"
         id="tspan212253"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212251"> Database</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,-14.287503,-5.2916667)"
       id="text45097"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099);fill:#000000;fill-opacity:1;stroke:none"
       x="42.939453"
       y="0"><tspan
         x="218.94336"
         y="758.01953"
         id="tspan212257"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212255">Web 
</tspan></tspan><tspan
         x="208.26367"
         y="798.01953"
         id="tspan212261"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212259">server</tspan></tspan></text>
    <ellipse
       style="fill:#cdf8ff;fill-opacity:0.681529;stroke:#000000;stroke-width:0.786137;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-7"
       cx="106.1193"
       cy="196.66667"
       rx="23.709324"
       ry="13.193636" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,37.478204,-6.1777104)"
       id="text45097-9"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-8);fill:#000000;fill-opacity:1;stroke:none"
       x="42.939453"
       y="0"><tspan
         x="228.51367"
         y="758.01953"
         id="tspan212265"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212263">API 
</tspan></tspan><tspan
         x="208.26367"
         y="798.01953"
         id="tspan212269"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212267">server</tspan></tspan></text>
    <ellipse
       style="fill:#f1dff7;fill-opacity:1;stroke:#000000;stroke-width:0.778699;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-7-0"
       cx="159.08586"
       cy="196.70529"
       rx="23.256241"
       ry="13.197355" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,90.901478,-6.1391114)"
       id="text45097-9-2"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-8-2);fill:#000000;fill-opacity:1;stroke:none"
       x="104.11133"
       y="0"><tspan
         x="220.70898"
         y="758.01953"
         id="tspan212273"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212271">Data
</tspan></tspan><tspan
         x="195.43555"
         y="798.01953"
         id="tspan212277"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212275">pipeline</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,1.0583333,10.583334)"
       id="text62482"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect62484);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="33.445312"
         y="978.39258"
         id="tspan212279">Security:        ★☆☆☆☆
</tspan><tspan
         x="33.445312"
         y="1028.3926"
         id="tspan212281">Performance: ★★★★★
</tspan></text>
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow)"
       d="m 47.195267,149.05722 6.214683,34.9539"
       id="path80708"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow)"
       d="m 159.08586,183.50832 6.52638,-34.0695"
       id="path80708-7"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow)"
       d="m 106.1193,183.47342 59.49294,-34.0346"
       id="path80708-7-1"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow)"
       d="m 53.40995,184.01112 112.20229,-34.5723"
       id="path80708-7-1-3"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2)"
       d="m 47.195267,149.05722 58.924033,34.4162"
       id="path80708-7-1-3-4"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-7)"
       d="M 76.683975,61.541214 47.195267,122.74269"
       id="path80708-7-1-3-4-4"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-7-5)"
       d="M 76.683975,61.541214 106.87472,122.59512"
       id="path80708-7-1-3-4-4-3"
       sodipodi:nodetypes="cc" />
    <ellipse
       style="fill:#fe0000;fill-opacity:0.213973;stroke:#000000;stroke-width:1;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path15701-5"
       cx="297.77563"
       cy="45.796322"
       rx="46.290901"
       ry="15.684086" />
    <ellipse
       style="fill:#e6fae0;fill-opacity:1;stroke:#000000;stroke-width:0.803215;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-8"
       cx="274.50162"
       cy="181.26019"
       rx="24.766687"
       ry="13.185097" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,228.75819,-89.272833)"
       id="text22680-10-2"
       style="font-style:normal;font-weight:normal;font-size:42.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-6-19);fill:#000000;fill-opacity:1;stroke:none"
       x="171.41656"
       y="0"><tspan
         x="176.29519"
         y="502.78518"
         id="tspan212285"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212283">Outside </tspan></tspan><tspan
         x="199.94104"
         y="556.11856"
         id="tspan212289"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212287">world</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,206.80406,-21.227476)"
       id="text45097-6"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-3);fill:#000000;fill-opacity:1;stroke:none"
       x="42.939453"
       y="0"><tspan
         x="218.94336"
         y="758.01953"
         id="tspan212293"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212291">Web 
</tspan></tspan><tspan
         x="208.26367"
         y="798.01953"
         id="tspan212297"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212295">server</tspan></tspan></text>
    <ellipse
       style="fill:#cdf8ff;fill-opacity:0.681529;stroke:#000000;stroke-width:0.786137;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-7-02"
       cx="327.21097"
       cy="180.73102"
       rx="23.709324"
       ry="13.193636" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,258.56976,-22.11352)"
       id="text45097-9-7"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-8-80);fill:#000000;fill-opacity:1;stroke:none"
       x="42.939453"
       y="0"><tspan
         x="228.51367"
         y="758.01953"
         id="tspan212301"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212299">API 
</tspan></tspan><tspan
         x="208.26367"
         y="798.01953"
         id="tspan212305"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212303">server</tspan></tspan></text>
    <ellipse
       style="fill:#f1dff7;fill-opacity:1;stroke:#000000;stroke-width:0.778699;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-7-0-6"
       cx="380.17752"
       cy="180.76965"
       rx="23.256241"
       ry="13.197355" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,311.99304,-22.074921)"
       id="text45097-9-2-1"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-8-2-5);fill:#000000;fill-opacity:1;stroke:none"
       x="104.11133"
       y="0"><tspan
         x="220.70898"
         y="758.01953"
         id="tspan212309"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212307">Data
</tspan></tspan><tspan
         x="195.43555"
         y="798.01953"
         id="tspan212313"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212311">pipeline</tspan></tspan></text>
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-6)"
       d="m 273.86151,143.17536 0.64,24.89988"
       id="path80708-3"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-6)"
       d="m 380.17742,167.57244 1.14045,-24.01546"
       id="path80708-7-2"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-6)"
       d="m 327.21086,167.53754 54.10701,-23.98056"
       id="path80708-7-1-1"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-6)"
       d="M 274.50151,168.07524 381.31787,143.55698"
       id="path80708-7-1-3-5"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-40)"
       d="m 273.86151,143.17536 53.34935,24.36218"
       id="path80708-7-1-3-4-99"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-7-46)"
       d="M 297.77563,61.480408 273.86151,116.86105"
       id="path80708-7-1-3-4-4-1"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-7-5-6)"
       d="m 297.77563,61.480408 30.24143,55.233122"
       id="path80708-7-1-3-4-4-3-4"
       sodipodi:nodetypes="cc" />
    <g
       id="g109050"
       transform="matrix(0.5344693,0,0,0.5344693,-278.93726,180.35567)">
      <g
         id="g109577"
         transform="matrix(0.32075687,0,0,0.32075687,557.90311,67.826461)">
        <path
           d="m 79.797436,18.8 c -0.5,-1.6 -1.7,-2.9 -3.2,-3.7 l -30.5,-14.6 c -0.8,-0.4 -1.7,-0.5 -2.5,-0.5 -0.8,0 -1.7,0 -2.5,0.2 l -30.5,14.7 c -1.5000002,0.7 -2.6000002,2 -3.0000002,3.7 L 0.09743584,51.5 c -0.3,1.7 0.1,3.4 1.09999996,4.8 L 22.297436,82.4 c 1.2,1.2 2.9,2 4.6,2.1 h 33.6 c 1.8,0.2 3.5,-0.6 4.6,-2.1 l 21.1,-26.1 c 1,-1.4 1.4,-3.1 1.2,-4.8 z"
           id="path10349"
           style="fill:#326de6" />
        <path
           d="m 75.097436,50.2 v 0 c -0.1,0 -0.2,0 -0.2,-0.1 0,-0.1 -0.2,-0.1 -0.4,-0.1 -0.4,-0.1 -0.8,-0.1 -1.2,-0.1 -0.2,0 -0.4,0 -0.6,-0.1 h -0.1 c -1.1,-0.1 -2.3,-0.3 -3.4,-0.6 -0.3,-0.1 -0.6,-0.4 -0.7,-0.7 0.1,0 0,0 0,0 v 0 l -0.8,-0.2 c 0.4,-2.9 0.2,-5.9 -0.4,-8.8 -0.7,-2.9 -1.9,-5.7 -3.5,-8.2 l 0.6,-0.6 v 0 -0.1 c 0,-0.3 0.1,-0.7 0.3,-0.9 0.9,-0.8 1.8,-1.4 2.8,-2 v 0 c 0.2,-0.1 0.4,-0.2 0.6,-0.3 0.4,-0.2 0.7,-0.4 1.1,-0.6 0.1,-0.1 0.2,-0.1 0.3,-0.2 0.1,-0.1 0,-0.1 0,-0.2 v 0 c 0.9,-0.7 1.1,-1.9 0.4,-2.8 -0.3,-0.4 -0.9,-0.7 -1.4,-0.7 -0.5,0 -1,0.2 -1.4,0.5 v 0 l -0.1,0.1 c -0.1,0.1 -0.2,0.2 -0.3,0.2 -0.3,0.3 -0.6,0.6 -0.8,0.9 -0.1,0.2 -0.3,0.3 -0.4,0.4 v 0 c -0.7,0.8 -1.6,1.6 -2.5,2.2 -0.2,0.1 -0.4,0.2 -0.6,0.2 -0.1,0 -0.3,0 -0.4,-0.1 h -0.1 l -0.8,0.5 c -0.8,-0.8 -1.7,-1.6 -2.5,-2.4 -3.7,-2.9 -8.3,-4.7 -13,-5.2 l -0.1,-0.8 v 0 0.1 c -0.3,-0.2 -0.4,-0.5 -0.5,-0.8 0,-1.1 0,-2.2 0.2,-3.4 v -0.1 c 0,-0.2 0.1,-0.4 0.1,-0.6 0.1,-0.4 0.1,-0.8 0.2,-1.2 v -0.6 0 c 0.1,-1 -0.7,-2 -1.7,-2.1 -0.6,-0.1 -1.2,0.2 -1.7,0.7 -0.4,0.4 -0.6,0.9 -0.6,1.4 v 0 0.5 c 0,0.4 0.1,0.8 0.2,1.2 0.1,0.2 0.1,0.4 0.1,0.6 v 0.1 c 0.2,1.1 0.2,2.2 0.2,3.4 -0.1,0.3 -0.2,0.6 -0.5,0.8 v 0.2 0 l -0.1,0.8 c -1.1,0.1 -2.2,0.3 -3.4,0.5 -4.7,1 -9,3.5 -12.3,7 l -0.6,-0.4 h -0.1 c -0.1,0 -0.2,0.1 -0.4,0.1 -0.2,0 -0.4,-0.1 -0.6,-0.2 -0.9,-0.7 -1.8,-1.5 -2.5,-2.3 v 0 c -0.1,-0.2 -0.3,-0.3 -0.4,-0.4 -0.3,-0.3 -0.5,-0.6 -0.8,-0.9 -0.1,-0.1 -0.2,-0.1 -0.3,-0.2 -0.1,-0.1 -0.1,-0.1 -0.1,-0.1 v 0 c -0.4,-0.3 -0.9,-0.5 -1.4,-0.5 -0.6,0 -1.1,0.2 -1.4,0.7 -0.6,0.9 -0.4,2.1 0.4,2.8 v 0 c 0.1,0 0.1,0.1 0.1,0.1 0,0 0.2,0.2 0.3,0.2 0.3,0.2 0.7,0.4 1.1,0.6 0.2,0.1 0.4,0.2 0.6,0.3 v 0 c 1,0.6 2,1.2 2.8,2 0.2,0.2 0.4,0.6 0.3,0.9 v -0.1 0 l 0.6,0.6 c -0.1,0.2 -0.2,0.3 -0.3,0.5 -3.1,4.9 -4.4,10.7 -3.5,16.4 l -0.8,0.2 v 0 c 0,0.1 -0.1,0.1 -0.1,0.1 -0.1,0.3 -0.4,0.5 -0.7,0.7 -1.1,0.3 -2.2,0.5 -3.4,0.6 v 0 c -0.2,0 -0.4,0 -0.6,0.1 -0.4,0 -0.8,0.1 -1.2,0.1 -0.1,0 -0.2,0.1 -0.4,0.1 -0.1,0 -0.1,0 -0.2,0.1 v 0 c -1.1,0.2 -1.8,1.2 -1.6,2.3 0,0 0,0 0,0 0.2,0.9 1.1,1.5 2,1.4 0.2,0 0.3,0 0.5,-0.1 v 0 c 0.1,0 0.1,0 0.1,-0.1 0,-0.1 0.3,0 0.4,0 0.4,-0.1 0.8,-0.3 1.1,-0.4 0.2,-0.1 0.4,-0.2 0.6,-0.2 h 0.1 c 1.1,-0.4 2.1,-0.7 3.3,-0.9 h 0.1 c 0.3,0 0.6,0.1 0.8,0.3 0.1,0 0.1,0.1 0.1,0.1 v 0 l 0.9,-0.1 c 1.5,4.6 4.3,8.7 8.2,11.7 0.9,0.7 1.7,1.3 2.7,1.8 l -0.5,0.7 v 0 c 0,0.1 0.1,0.1 0.1,0.1 0.2,0.3 0.2,0.7 0.1,1 -0.4,1 -1,2 -1.6,2.9 v 0.1 c -0.1,0.2 -0.2,0.3 -0.4,0.5 -0.2,0.2 -0.4,0.6 -0.7,1 -0.1,0.1 -0.1,0.2 -0.2,0.3 0,0 0,0.1 -0.1,0.1 v 0 c -0.5,1 -0.1,2.2 0.8,2.7 0.2,0.1 0.5,0.2 0.7,0.2 0.8,0 1.5,-0.5 1.9,-1.2 v 0 c 0,0 0,-0.1 0.1,-0.1 0,-0.1 0.1,-0.2 0.2,-0.3 0.1,-0.4 0.3,-0.7 0.4,-1.1 l 0.2,-0.6 v 0 c 0.3,-1.1 0.8,-2.1 1.3,-3.1 0.2,-0.3 0.5,-0.5 0.8,-0.6 0.1,0 0.1,0 0.1,-0.1 v 0 l 0.4,-0.8 c 2.8,1.1 5.7,1.6 8.7,1.6 1.8,0 3.6,-0.2 5.4,-0.7 1.1,-0.2 2.2,-0.6 3.2,-0.9 l 0.4,0.7 v 0 c 0.1,0 0.1,0 0.1,0.1 0.3,0.1 0.6,0.3 0.8,0.6 0.5,1 1,2 1.3,3.1 v 0.1 l 0.2,0.6 c 0.1,0.4 0.2,0.8 0.4,1.1 0.1,0.1 0.1,0.2 0.2,0.3 0,0 0,0.1 0.1,0.1 v 0 c 0.4,0.7 1.1,1.2 1.9,1.2 0.3,0 0.5,-0.1 0.8,-0.2 0.4,-0.2 0.8,-0.6 0.9,-1.1 0.1,-0.5 0.1,-1 -0.1,-1.5 v 0 c 0,-0.1 -0.1,-0.1 -0.1,-0.1 0,-0.1 -0.1,-0.2 -0.2,-0.3 -0.2,-0.4 -0.4,-0.7 -0.7,-1 -0.1,-0.2 -0.2,-0.3 -0.4,-0.5 v -0.2 c -0.7,-0.9 -1.2,-1.9 -1.6,-2.9 -0.1,-0.3 -0.1,-0.7 0.1,-1 0,-0.1 0.1,-0.1 0.1,-0.1 v 0 l -0.3,-0.8 c 5.1,-3.1 9,-7.9 10.8,-13.6 l 0.8,0.1 v 0 c 0.1,0 0.1,-0.1 0.1,-0.1 0.2,-0.2 0.5,-0.3 0.8,-0.3 h 0.1 c 1.1,0.2 2.2,0.5 3.2,0.9 h 0.1 c 0.2,0.1 0.4,0.2 0.6,0.2 0.4,0.2 0.7,0.4 1.1,0.5 0.1,0 0.2,0.1 0.4,0.1 0.1,0 0.1,0 0.2,0.1 v 0 c 0.2,0.1 0.3,0.1 0.5,0.1 0.9,0 1.7,-0.6 2,-1.4 -0.1,-1.1 -0.9,-1.9 -1.8,-2.1 z m -28.9,-3.1 -2.7,1.3 -2.7,-1.3 -0.7,-2.9 1.9,-2.4 h 3 l 1.9,2.4 z m 16.3,-6.5 c 0.5,2.1 0.6,4.2 0.4,6.3 l -9.5,-2.7 v 0 c -0.9,-0.2 -1.4,-1.1 -1.2,-2 0.1,-0.3 0.2,-0.5 0.4,-0.7 l 7.5,-6.8 c 1.1,1.8 1.9,3.8 2.4,5.9 z m -5.4,-9.6 -8.2,5.8 c -0.7,0.4 -1.7,0.3 -2.2,-0.4 -0.2,-0.2 -0.3,-0.4 -0.3,-0.7 l -0.6,-10.1 c 4.4,0.5 8.3,2.4 11.3,5.4 z m -18.1,-5.1 2,-0.4 -0.5,10 v 0 c 0,0.9 -0.8,1.6 -1.7,1.6 -0.3,0 -0.5,-0.1 -0.8,-0.2 l -8.3,-5.9 c 2.6,-2.5 5.8,-4.3 9.3,-5.1 z m -12.2,8.8 7.4,6.6 v 0 c 0.7,0.6 0.8,1.6 0.2,2.3 -0.2,0.3 -0.4,0.4 -0.8,0.5 l -9.7,2.8 c -0.3,-4.2 0.7,-8.5 2.9,-12.2 z m -1.7,16.9 9.9,-1.7 c 0.8,0 1.6,0.5 1.7,1.3 0.1,0.3 0.1,0.7 -0.1,1 v 0 l -3.8,9.2 c -3.5,-2.3 -6.3,-5.8 -7.7,-9.8 z m 22.7,12.4 c -1.4,0.3 -2.8,0.5 -4.3,0.5 -2.1,0 -4.3,-0.4 -6.3,-1 l 4.9,-8.9 c 0.5,-0.6 1.3,-0.8 2,-0.4 0.3,0.2 0.5,0.4 0.8,0.7 v 0 l 4.8,8.7 c -0.6,0.1 -1.2,0.2 -1.9,0.4 z m 12.2,-8.7 c -1.5,2.4 -3.6,4.5 -6,6 l -3.9,-9.4 c -0.2,-0.8 0.2,-1.6 0.9,-1.9 0.3,-0.1 0.6,-0.2 0.9,-0.2 l 10,1.7 c -0.5,1.4 -1.1,2.7 -1.9,3.8 z"
           id="path10351"
           style="fill:#ffffff" />
      </g>
      <path
         d="m 563.92292,102.87075 v 22.43662 h -22.43659 v -22.43662 z m -11.21829,5.17769 c -2.00459,0 -3.7127,1.26935 -4.36437,3.04811 v 0 l -0.18016,3.2e-4 c -2.25604,0 -4.08493,1.8289 -4.08493,4.08493 0,2.25604 1.82889,4.08494 4.08493,4.08494 v 0 h 9.08909 c 2.25604,0 4.0849,-1.8289 4.0849,-4.08494 0,-2.25603 -1.82886,-4.08493 -4.0849,-4.08493 v 0 l -0.18015,-3.2e-4 c -0.65169,-1.77876 -2.35979,-3.04811 -4.36437,-3.04811 z"
         fill="#4285f4"
         id="path119683"
         style="fill-rule:evenodd;stroke:none;stroke-width:1.12183" />
      <text
         xml:space="preserve"
         transform="matrix(0.49503935,0,0,0.49503935,518.92552,-333.48782)"
         id="text124983"
         style="font-style:normal;font-weight:normal;font-size:64px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect124985);fill:#000000;fill-opacity:1;stroke:none"
         x="380.69757"
         y="0"><tspan
           x="150.08691"
           y="68.839844"
           id="tspan212317"><tspan
             style="font-weight:bold;text-align:center;text-anchor:middle"
             id="tspan212315">No sandboxing</tspan></tspan></text>
      <text
         xml:space="preserve"
         transform="matrix(0.49503935,0,0,0.49503935,522.74969,232.90271)"
         id="text124983-1"
         style="font-style:normal;font-weight:normal;font-size:64px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect124985-08);fill:#000000;fill-opacity:1;stroke:none"
         x="733.30695"
         y="0"><tspan
           x="60.274414"
           y="68.839844"
           id="tspan212321"><tspan
             style="font-weight:bold;text-align:center;text-anchor:middle"
             id="tspan212319">Partially sandboxed</tspan></tspan></text>
      <text
         xml:space="preserve"
         transform="matrix(0.49503935,0,0,0.49503935,925.938,230.22938)"
         id="text124983-1-2"
         style="font-style:normal;font-weight:normal;font-size:64px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect124985-08-0);fill:#000000;fill-opacity:1;stroke:none"
         x="733.30695"
         y="0"><tspan
           x="57.396484"
           y="68.839844"
           id="tspan212325"><tspan
             style="font-weight:bold;text-align:center;text-anchor:middle"
             id="tspan212323">Partially sandboxed
</tspan></tspan><tspan
           x="42.943359"
           y="148.83984"
           id="tspan212329"><tspan
             style="font-weight:bold;text-align:center;text-anchor:middle"
             id="tspan212327">with hosted services</tspan></tspan></text>
      <text
         xml:space="preserve"
         transform="matrix(0.48312597,0,0,0.49503935,916.30801,-331.74408)"
         id="text124983-3"
         style="font-style:normal;font-weight:normal;font-size:64px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect124985-0);fill:#000000;fill-opacity:1;stroke:none"
         x="380.69757"
         y="0"><tspan
           x="29.550781"
           y="68.839844"
           id="tspan212333"><tspan
             style="font-weight:bold;text-align:center;text-anchor:middle"
             id="tspan212331">Completely sandboxed</tspan></tspan></text>
      <text
         xml:space="preserve"
         transform="matrix(0.49503935,0,0,0.49503935,1130.9253,-53.168673)"
         id="text4916-7"
         style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-8);fill:#000000;fill-opacity:1;stroke:none"><tspan
           x="40.033203"
           y="354.32031"
           id="tspan212335">Your cloud stack</tspan></text>
      <text
         xml:space="preserve"
         transform="matrix(0.49503935,0,0,0.49503935,1089.2279,-84.585143)"
         id="text4916-5-9"
         style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-7-5);fill:#000000;fill-opacity:1;stroke:none"><tspan
           x="40.033203"
           y="354.32031"
           id="tspan212337">Kubernetes Cluster</tspan></text>
      <text
         xml:space="preserve"
         transform="matrix(0.49503935,0,0,0.49503935,939.65817,-315.86612)"
         id="text62482-3"
         style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect62484-0);fill:#000000;fill-opacity:1;stroke:none"><tspan
           x="33.445312"
           y="978.39258"
           id="tspan212339">Security:        ★★★★★
</tspan><tspan
           x="33.445312"
           y="1028.3926"
           id="tspan212341">Performance: ★☆☆☆☆
</tspan></text>
      <image
         width="35.862225"
         height="39.05415"
         preserveAspectRatio="none"
         xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAL8AAADQCAYAAABWSLCJAAApKHpUWHRSYXcgcHJvZmlsZSB0eXBl IGV4aWYAAHjapZxZklw5b4XfuQovgfOwHIJDhHfg5fs7zJS6W9L/0LYUUpWqsm7yEsAZQFy58z// fd1/8Wvk2l0urddRq+dXHnnEySfdf37N93fw+f39ftn9fi/88+tuf7/uI19KfEyff/b6ff2Pr4ef F/h8mHxW/nahvr7fsH9+Y+Tv9fsvF/q+UdKKIp98V+LG90Ipfr4RvheYn9vydfT2j1s7n48/76R/ /jj9ZT++Wr4v/uXfubF7u/A+KcaTQvL8ndJ3AUl/okuTT/r7u/DCkBqfZ76lr/xYCRvyp336+Wuw oqul5j++6B9ROfbnaP34zP0arRy/L0m/bHL9+fGPX3eh/Dkqb+v/9s65fz+L//z6PDF9VvTL7uvP vbvfd8/cxcyVra7fm/pxK+8zXscNZ12oO5ZWfeNP4RLt/R787mT1IhW2X974vcIIkXDdkMMOM9xw 3scVFkvM8bjY+CTGFdP7Yk8tjriS4pf1O9zY0kibyMa0XtiJ6c+1hPe2wy/33q3zzjvw0hi4WFBe /Nvf7t/+wL0qhRD8d/NJC9YVozabZShy+puXEZFwv5ta3gb/+P3rL8U1EcGiXVaJDDbWPpewEv5C gvQCnXhh4eOnBkPb3wuwRbx1YTEhEQGiFlIJNfgWYwuBjewEaLL0mHI0IhBKiZtFxpxSJTY96q35 kRbeS2OJfNnxdcCMSJRUqbNOhCbByrmQPy13cmiWVHIppZZWehll1lRzLbXWVgWKs6WWXSutttZ6 G2321HMvvfbWex99jjgSoFlGHW30McacvOfkypOfnrxgTouWLFtxVq1Zt2FzkT4rr7LqaquvseaO O23wY9fddt9jzxMOqXTyKaeedvoZZ15S7SZ38y233nb7HXf+jFr4lu2vv/9F1MI3avFFSi9sP6PG V1v7cYkgOCmKGQGLLgci3hQCEjoqZr6HnKMip5j5EamKEllkUcx2UMSIYD4hlht+xM7FT0QVuf9X 3FzL/4hb/L9Gzil0/zJyv8ftT1HboqH1IvapQm2qT1Qf3z99xj5Fdr99dHEeVjR2XMesj915h0q+ wJD3gGwlTUuetay8eyQSZZ0U9yp1rVCL38YKSiNq12drsc7FRgJoY9fKp4n177BA+hHzaWn1ckap JwGXtd/EBoZ1WWezMe1Wc+xhaJYml9/eFvvWiA8/Xs3CTumEdLuJ8/ywyOJL2ueEzi7kuS+x0BcW wNZz7jUSIbC2zplvY1tKLG3ePUCxYhf+S63NlYu/k5cks1v2LnN7XpBRDfc4s0K86r1kZGl39JTt WMijEvzSLyIg2r4E0QNFvNkh8OO0uwmPQtD77KdcB1xPAnNtbNJ3Z32T9Xt9jCXoqiWTFa0Hi72W OBcBFVIUn/Yo7MrZyU/HZRustZu1zRv7NHwc1fJunt3Ze0S/eP+w+jzVeC945sRhgOMM/QSb+exT kgt7tfve/9guzSrlsBFLFg43NqmURY20Wvgp38qcJZ4xWgqHAvCQ6KK6qk03t0SL3R1ru5H1Gtuz D0l0VmfX8xns5s2H5OrxtLgo85iDEJvsIii5pT2zSwTpkEFs/GVRVCS6BpIGs1cnVMSFtIrsE/We WHAyaU9l9Kmhs4tF2qy4H5/8p4/ppD7zGFZXVRSNVN9nk0vcbZjsF8Es9bpt22axLG5pBG1uEhJh pUK5MEzNqd5ZyXne/65zQZ/FdoVbpykZAQZqfHJrIxRSI2fK7Uaqm8RISuxtd4VyfZ17bijp2sq5 LVLtRm7RVoR2be9F2k4D/EkNEiDVACyMCi6lRW7dRvmWfM3vQWX5WyjpxlfInhZJ+xMGS/ZEvCFb tgtnQX02jULhFshbMJqfA1woicZ7ANul7hAXyF1B32rUDdlAvhmgyP6AlcdRGq0kMFiCyKjy3T8l TdYdUoKkqj2Rfwe4KkDnIOuH1g8nWKZUQB4AyZFAIFILg6ohTws7UQ0cY4u82Z4NiN9r6ofJkNIX So78a4IT6z3GvcFpbq2FBBwtQBxwZ5Gef2oTNpU7MgW+alnKmjjzb8jY2QqweN7pXSVdp++HYjcQ olNAgZLbbbDdvR3PdwLYAVSB9cB2POQ5KH2EVPn0dOO8uTneaSZ4op0AXxJHNubCKABXL3sMEm/n tSs3B4XximXgRwuCj1OinU2Nx35c68DIKKFQqR2MwJOwGq61E6vZcBYFw8sB57NGg84AFba4GFxA 9O+gguqObhWSAgKqVFxi/QAe+D59yYOFAvQAJUGv7OGBEBawjTehKHcXxFVYaYDV0ZWx71PBBxcE lcF12kHAYlLUm9I4fYP7rZjKhrxIZKbeY8Zx0WKQR4ehXSCCYxSWk3cyuP8Tkexf1Rb/10d0GznB 7aVYeUcKcwAsA1UMPy43X64CtDaxA71CkMXAVUzVxEWmUyPAQcGDGBci4pYgOCOdD7gFLKHEgQhz Al1ysV1eF6A3YC1RF16QdPepYslwZgb/Zqyd7KPMJqUMsICRs4Iw0NZxsDdwCKSwVMXpAiYIiOf1 pAkymev31Y1zvbW5cNlWCOYs0AEEDHoAE46Uwx9QwLfWtGZGdlwwOfASXMAAktgI1AWktIwkA0gy iZRjGbzXBrq1hNwd6gNFRE22S961AUTPuWytCSeye0gpBMv1xLMCWHuSDiQIeoxyva2m3QtojYgg 7CeSqBN6hEu20NOPcDAn5OhtpHNkyx7Ps0+NS7MY4Tvc1RIiZ/NtcwQkD7i0T6tn2ViWzwLkM1BK LVKGETzseCyypfSDX1J5A0cgtaHxZ4FvWnPnsBu9wXKp7gQxwV/rIENIs42j7oUAkw0VcRZt9Ppb fvGxhOV2WlwhID8A78r77TLAdnUzhiinnEee+IteIKSYQdO1jW1lixGkq0toVO/KZkvJC7A8Izu4 rn4wI/KkKCbEP0k+NDIVEwqS795AUaFu7qPczD/Y7OtgaCLakGx+rLBjBMPB6xTgCQLChnFD0Ho3 UsQm5GCWL5bp5IyWm7wyw+XLsT/HKEpSjsixrjH77QhLdEDmUgi7Cl/HASHwGSBKPZxdzoI/Vov6 G5wfru7Ky0haIPGg01PBXKyLUV7USl4DVLyFYIG4laRmt1DZCVQlo21HWGQYis5Nn8T9pt0wFkNV +JXOsUZipR84USQnu5CJwsrgGLxiqtpD1ksFJKdNW0Y1EFgpiF3x3qOxZnWOWBjymhQDlgvif5Gl VNfAZaABCAnAjPKeJbs7w0SLjwV4tn+iUZFcoQaHSuJKDrRABUF4a+zhAwagUv7onIQ+QkBTMlR8 kUJGO1K5SMgMgakVADVgAGJlr1+nIUtZoNoooZWBsQ8fU7uukGpAPJCXOkmHABYyoUmm4gUFYKyS IUJYjSeGIP9McfF+FPRmH8XQa2/HW7QNI4BgQ0ATiop4sUNcskPK3vrOs0Vv5BiGOd1VoelGhNjj xn3a2gNPC4Tjral4dCZ3165hdSUzJrrHK01Bksi/bvsqzSZ6D2nFxCpRT8GbH46MRSsFAMgIxm0U CmproGAH5gQwoyTUbqgwPf6uiXElGpZeemRbB3HF9x+PlmLjIdrr+UKVzkJN+TYGe5MGwPlwSg6l YrtIWvb0LBgSgJZEo9brcMi1bU/3pBC7fKOgBjIjgZ/i9H9TnHzED5VbIKHt8S/nNu4GqxgdMhdZ FKifJYPnCWhlu1BpYaRNwSb2XTGWGEf5l8GWn6w7g2myXB35s70T+wLgRzVr3D6iH8kgPk0kOjAh ykWY6Pa6cgh6b4MwQ8ixoLqhzJjGcdAlSYjuwLhBgyyurEcD6OJKxSEbQSKkBcAJV8L9Sqx7VNOX Ak9rY2YbeZR4g7XuRMQsg4ba5d8ErSmLu9C9sysslFKy1QJUN/DplOXFzyxAmlzArnMRD1mgGngJ KAS8b93GPOhDVGEvasFwT3hqLgAZYiEEryYkghHIkYOlc+RoRYXMCcMow0GKmcFAwOCI0oratYAD ygngy5U8QyFAHSg3q3gfwGOTJC4ONCb7mXACJB1sxcK8wXZk4I0t8+/CDVxUCMKfJUI3mMCMaYXs 7MKesPhwiFg4kh9jc9FTUl+Sl+wkAMC1F76uHbZ1AXcmkQ5hqR2B46+kLU6SVZ3gsrxZShvUe64N xxwBH0yAZys61I3hQN8ATBnaE2ZTQuAK2IdbhgvRh4OoJdReUgxAMfYQMe1x/hhoyGLxzrAT5Msa MAbIZrhhVZh7asdBnxATyLiuAWxk6sD4y3cahlS4AtnZQU0q2hQp1BC3WpRc7WQ8Izke/FJUb0Q5 YVwxNVAZih4sByQh5d4xACNTowDx0Y4etX9YGvS62akqDcTu14DSJQtFm7gEB517kL3y2sR9BMlZ zAuJ7KWwgEOkgJA5YW4Rp9zAZt99aPi3CrxREAspKLu+kMM5e/w4EhF9sfDAZ0jhTJjaI51V38Dn wP1V8BfoWFumG1fGW3gSb7uVoRYiH3WN6yl5UGLVISEZSMCBxZKV66Gq2xU7ogqNpFJP46KQwPpw mjnBPCQK4/NCKgvDQYqSy9w5cpWUH7qdUtQRDQ9coPd6l/ZBnSd5jrYTCEnEKADUAmpRwabGSOWj KyGQwAH0CjgRFECkVIisHnBGeu5PU6Ogt65jp0eOSNMD/IAooVsyWQbU4QK2KWM4BDnJGyHQuXus KlWAcQwN3FkLqMddOrIC/MG+cWlACAZqpAxMQ2bKRgUpbVVGqzfhNQM7c0GSgGM00trAtoyid8Bf JP+fjkPo++fx2NGIDJahwQU09SHgEFbMqg0bSXagwuFkDAGIc4OZQ6Gjz9Hh0B+eAVMcO6kfd8Jb E0g2S63nqM6dyhykuHV7UDUu0WjCK/L2CC2MMTcDrRDlkrC9ZRJPtJEyOKr+Cc0RTaN0cF0TlpLg 6Q3eJ2igEZuG9Mu1E4ona9UpQP+chDqHPZAFltEQOMipxqMZtxERUohOTDbcf0WQoaH+irvnGhZq QNToiJXYUxiCF++0BfjUCdsIL+JrlGyb2uIuN3eFTiw4hiSVaw6ul00nM0oYyAeYJ8140GFZO3fU zIJ3cQ61CUU9m9QhDzCIHEAzo9SQVovN1gLrGgoOyE20GjsT2qFUC9R/hloDYMNWzgJbWRdiY7E2 FB2B3dLCbvPSDdyUqu/oxhdSLD8m8VRwljsryaAVnCssAj29vTkD2UPMwueVjoC/nth7g/YuA0zp mzDOkw9Lq5dW/SxowQ+HbNmYuhk2VadQT8d7wgNwKpli+GOYBTUwKAdgnXo6Jn7skCEOkTig0gUo YNnnbcBc4jPhfhKYZF+kD2WtOGCB12ow1ohaQWkI4mbqaKtTB6U9pwt2ywmjJ9A42D9HGm3xa/YK MeXjxyaPNvuIGLU8UGLkmMovYDrUM6k4JXAqSBjug6QzAMmBlCgH2YyiT7h3EhOiCBAXthbiYgk4 vg2KoooaZJxxJjAnVi2wT1gmI6ccFg/Ske4iemyNSAdEf6Kqjb71a2RV46oUG2lKAe6kji9oHs+h mtjBhKpVE3BgK6kLBAKqAWvTSEmcAvqHeIJvwxpbMUX/2Fm1ZKrkNW4GcG1yIQ5BvBAtHgKgUA7+ MRc2h5JUv6kO5NDAxyQqGqFiJxA+JAb2v3mMWwsXlQ3Pu4KKNXEMts2iahGyHY1IDAAopeEhDxb8 6RYuwA0Ir+vrSHP3JVVgLrqI/YF2Z/QHqAqIJXQUhI/SRbD0pWMTgDc2lTbZBKbX26F49btvxpRX 5O/oDobv4LHqGCtAUNjKaJRZHqTP86B4j4FQRrQgLXF55B/CTxpv8UPoisFdONGZytLYqafsSUt+ AFAlTbAR5AosSAbzBer9KVG1rXCHOuFkX4ZOrSeUzc8F3AeWvwtaVqQwSMaKd44FAQfREbaI0bxs PrmOGEVuk7z83edWf1jHGRhZ/wPyf0J/RhaTmCQNdwnUkl8QCEyd1dvDq3f/evF5ItkF5dWhcGUQ 0A0ScmqZePAeroO8ok5pJD0oYL5aiTUrJObQM0hOHp0lwsICFBdQ5Qme2QvUIWQ4jdLlcltl79Bs OjWCvAPwQ/WDTxCeSUUWNuD4D+CA2Q1XDmJGIrsApaXfAXAND9rQxmQcwrgfNtNTDpgQlIDp4Gki l5u0V0QDwbQdxY13mUAQthau7uoRRgkt9aupGgAJrMGPyI5nnPQ9OjFVpwKEQqd5pB/gBup4rSXJ e6jvr5h6ZO481EbC7bamYx51MUF7hCh7AkHark3UBPGO7Y5OqxDebCGwhM4GNxHQauyX/imNw65i 2yB52RbtqKSMpd7UBWABO6oRZTp5qE0nE2y8byxXpx8RIIyyLiXiUTw0b2ywjn2vdg3HuUuOZPfz d9VL1sAc3bcKSFDqGNoMGcfD7e6kbhBek7yEYEbCKUtGH6or51hVMuwp1pALSY6js0EpsdWimDZ1 oBBnQJE7ruQ7VVX4FAPIlyLs/Y5rGnoORoWdsdfUGjYHj0H+E8tBRFHIU70AMAX46ve1CPBPr/Pz 6pE3wbp1eKPArIbMidGZOvM44SOdNXRGyMqJVQF48KIZ01l1yowWsYsFmAfZgiElUuWNl6DS0NvX wZz8cEZcCkCUfmk0QnyIWkQJjdc2z89eSPaypWeiTngjj2EAn183BX3k2djXvZY4BbXRXdAFojFT UCQ7OT4VcTUUyL8DL3sSUtoWMNGpaKQYBi6bGAq9pMh0pgaBNUSezr8xPFnnGF9okPMWrGA8x/Sk aFRXGt0AvCZMDf5iAThcLWCvPCCDraAudEYCeXisZGpRDlMLs9dx0KwHOHsVWtn3eRzmnfCvqD4b zAKBy5Jm0ol3ACMHcoz3GC8bIV2WHdU8xocEP2X/eOOL0EJsH3VPUEeQDSpLh15xXNUBmuJDFg2m ++2kC08BQcPflOtFsHuYdLB9QOfpqQMVHgMwJ+sksxCuzy7ID6AvcbW8qlq56iCjzXBEmDQqAQep pT17KKxoa8LLqNnXG5MwE/yhDSlutlndOZ1nQB9oTvW2dKqJuCtUvzrNE394BeTcLrmC0U5P2p9u ePNWF5oG79HQj1cnA6UhKe4YSy4oSBjCIlh5EFbNYfTTM/4e9CNbQfUE4aGi7iG7dWYOoRW1wIKQ aqJQmwp+h5oQ7GMOncY2rDq3IpFaDanOaljkw+QbI2rkkQQimOuzTnRvU48+If4RG90pv6/OxCQ2 5R2R97OBvDphhoTV8CIcRNUIVFRjRZ2IKSeu1g2CABicFaj1cssNj1rJQ7IVNJUUoVyg1ammaqpA yDk6+78XyQBwnJ5l5PCtQGAfMVO0HUEjNi6YAeQ5CK0etJA4q7XHTwIHsHKhpNGfhJm/MxZzj86O Tfa4jMe08ugVxlM3H5RUxmSMGH/vBvrqjL08Xh1PU+uI/X3ElbOvpvNujxjtCqV2NSP4WEHAWUGl C3x55GdiD0Q72arOIXr7as6iaUrHf1oBJB0iYqtd0FJVjSWwQ0YppEMI1aCGTWW1l0yN6CSEvN55 Gmg6o47TTlIDoLgaBoSrZhcpGcCujkNtISIgzXTkBx7DwIS66xD54nnBJ0pcB6bDqnz0rSW4rfYK DPWacVQI2aUOWWBxuB6SG0F+NdvR7C04dcKJGqAm7bXOT6cm93a9IF9Oa7lz6dpzizqKVpMBfU19 A8GKLlyXv2ZIQ2gQSQbdcCOQ5m5E2iFCEI0DMsiU58cjn/rz9CPvqq4iBrpgLfOnsYw7qKQ0tQTH fPIQGIm4rSQnPi08A0VsfE8fK6XenI861Snq0kk0eMTH4D7zUYpMtUJLtexAwP0BrzXGQW8jq2Gy Wzw7gkjPT/ck4Q/vQOE03C7Qw1cQvNsoRna9XIcWw6fgwgJesm0l+glLLT4N6+FKM2Q3ravXRgYi znVIC6W0z7EUmcOya3Avy0ELCL+Uc8mmoJ4LPE1JDcAYSWQwT3n4pFYYuZGRKCRgl4/AtuE/zE3p AsQsdv7iN9iroJXV54gRwXj4SDU1+/SQ1YzIOq6QsEE7IxJQSATdgeo691IZaSDN2DidDyZU0hoo gk7WZR0PRSUSUIIeEnujAjHZWe2cCM1cN6jkZqzE2EhITocOOqNPsPhW18w8FJeS3zOjyY4GFDQk Q/x00I67ByA0E0FpUQGas6u7RhQlNRk36C62fFxPrdlMoUU1nDEb345M48W4H8Qb7926OhGCUE2r CBcKlonAIkfTUh/5aIzi4QnbsyTlQojfvNUQ39WBHL484NcyYckR4kNkZI+WRnpBTgFlJ2Fhw7pO 8TwFl1UzuFKKA122hNVz2ERhTo1WmU6RALZl+40EoFxGADGqRjbQI+yqcf/4jjY0psE+k7oZjFns Aixa5UWyiBaZSwJdnZSXiSbSyV7POpxcEONtyXQadSURCLFaIVkthB1xupqX6IeEPJSvgH6od575 IbiYIkX9kdTrew4r1zWmhgUyrErM53l7O9WvbdxxdWNR+gfPQsI+7XVz1lEODl4IeUzHIYRdqqB0 Ag4u653I4hu7ziVwCQTcaeSnBtRMYEfYR6loAA4WhlI0ooHuoop4A9wH1yGOOCBvhX+1L9povMWd roKHiLImmdC8XWGv8vpibP6hlueRptVY4UjqrOEcSLJDCmFExlCfVP0jsohS4I8akwAMSCkEkkGL mlJI4jqNRwe1RXRS2Opv4sD9UAcAMCoacA+oOkxJl9eOUeCmDqTOQXWCTXx9oeDDa6YSCAiiDOSB K28s4SaZv4HX7FA+wquHnsnto341noi0rVv3H+rbL4lLqSlSHfZmV4vbGujheyi4uTQL0kGJx1E4 e+oada6BkIZ91UywAEmN/cEuSy5Db4A96tSVrnEJndNEonh6fzmDn0bXNBBToTU18+EzjCHWegTN Ecn0HCzt2Ozs5EIaLaMmLr5fDfMAp8bry9obzRGAD/Cuqov0LMY7f1HXNL+OTYMBfR0qKbdFcxa9 QghyzRE0fAi+xSTVnNhrpKdX4zjoR45aM0nHmZ18sjfAoTx2aOm69/7hKbJv4PsNSPurTuKOQQSO sdhIRiIifiA4pHqmJoDDqmmuxB5BEhQFJdm1jUEn2URlSnvwDfv0FKyG3wbKkKNRHUZ8BW7c6cvx 04IYmj7hPUyTAyQojK5R+qlOW9Hwm843QXsUcjq4jmctVtJpxSkONRxUl5ARUG6aD7l8hBW7GqyJ fFqyKDWKHI8u+JFZfXEj6pez7TCT01Al97i/p9gUfgEcvNzlc0tK9gTsDGG9Tg0Od6XD4UOme6l+ y6/nT8pbO1lZg6QfFLI6FbUC4riA9dli4FwNuMeto+Be3hmBalGTI1JVhc0OGyBWjwe+VJ8DZNqv aShzptYg5bfkHkKPjTtBaqotSx1Ir4d80Al4kRB02KjGOkaMwm0E7wxglSsBHvEG9n6COXjBqcZi yvWodMp6E4hDowPJDwdbod7MgyoFZE8SRPDt8fqax0UVrhzgO+QeNnwBFfKhEtkadwi8E3zVzRVJ rMJWQhJHJ1wZoYIClMHzahYdG7yZpv/QLZ8kIvfWr5No7pT0pksgXPTTAsneSFCYVKmeGTiSOIL5 Z5bB/I7I7EsDWmUjeTQcIjXr9lSTp0oWYqE1c8MuYTJQtdzsNNiX1NnI1621wvJqLEENSKTJu8SS LcGcDi4d/WPzTv/Bxv4PE1rk4ZKkzRs9q5HDoyggiJd4pWmK9YGDVpuqRkz69rw9oKdxPdXp0bDc Dio9VDH0J2ihiNPqlPD3IMpJuXgwmchvnfwfLxgDMPDgZA2bib9Slwdzw+1+HKrmE8+PxVYMyNCM Flao14Ok1LC6RMip+cHT8UAG1BA0tKcJCXTqxolBbbH4J43ZXvwSjnNB2cbaudJoETWss/uJ7NOg lVYaB7ilARfR8lyid58199OA/ApCZx3hH7yIhIJuG2jVPKkGu6bpnAuOJLdAIizHuGqVyFcgUWUZ KG0y15L63BtD17wD9wOeSj1T5D+WO4u7MKxq2c0qIyGpD6mbz910Fus1drNhigAJS5DkMr0rmoyp 6tJHNawQMBRrBig7cLmyCAqSL08Jo001uhrQnTq2P2gjBNAMddXj2L2iWWcYmjtYSD6d8i/5ILbu qvmsczP02+cUYJbfh3iV4w4hb2XDt1GkpREbsSjeRzXN5hb0vNrAR6egXNpXjb/yhx+SKNMzAbjs gc5GWdV6sxwquzEIGOvmW2z2G+6nzEzV78G5qOG1Bb/BT5dKlDX1GzdQ3DrcEMkOWqE6NAQpk85r QnwTatZiwWIsWB1CgLKPerul97+XPwyLO5osepITqJbxhu1B59fiJydDQv2BfZF46vmHN3lEMCi7 TZoF2xqrk5l3kkvUjqaz0QMPMPY7OYU2xse5BoQj3nN/3kYz3IA0oPoeCIBZNGV4HEWZQEAKK6tt AICMXif6BFPFmmCCj7JMOCY4EcSEBwkqMcMI6HAKDQ7cuvqGIwFlil/j/ev1roCWqyPJRpkNUvPu FJDBqK5DmiESJJlU04D6M3NEDdNIGsvRo4MhvSgRqIOsxfXBAKqFjPcmswK+oC2oTsoRGbUWdRr7 Nl7tNDKqURHN/oemg4ItcSw7w+7WVg/SiTWjyXWaD6I9CQmEst9vIHtMLPfl1oY64rGehkQfWABS Jya+vNBwYLLUJ2Kr6ykc5CJ1U8X4QzYAjyrL6CFMh5j8UgMQpXOP9pnsQfmhLcRlL4ig90Cbxzcw gcRIQ2dU3DBWHoHJrQ0ShjxL/nX0NjwjUQaaw0w6/OEWkUnariMXIdf+OTFmX2peXse4G/iRzkZT ZhkNDDTC7xxdAV4xdPfIlBDXtiFxkjVkd6YO8KFKXDfJOlPndkdxRCgujZvBJhq0peiQ4SRDjBp6 B/vMoxnhnie4RsY2YNU07TxhbLB+4H5fQ3Mh3a2qrwahB7lzcNU06QrhGn5ds1/QmWbWhFsaLIJD Eyn1Oe3y4l4Q0rfehdAYmB4/x4TedLCCE1SvMWMrAvdUqIo3n/w9YhrkJ0mdPE58Y/wwYQk7w0Um lCbMiUBGR1XCxhqfC1fjspqoF7iOofM1zYW+riYpAernNh07SC2rpUjtJxaFzxwaKQlA2xDz4xmn JuwjDCG7rlFu9bDtII11lKWRPA0x4x9zCxnvj1VtGvxTcI+SRWp46nmYAYaGGjWJnMkehB+IWnS2 eDXFyEvcNHwUCSI6eNNX0BdKSToBQDsjVDZ1CSi5bN5LZ4tIkjeTSiTeCJbOYp0O7qvmzH3YmnRr B9O9gCbNK8JNqHUNbJKWW32uGzQrfZK0etTBvahdLT2HEA64mqIOa4Fm2PHXn0Ur9aQ5B2pnmRw8 NbRY2oar5+eZIyA2sMNwBA5S/VOCisfQYEk1TLqpl9SlqSq+Xs9afEaYe1dLS0/awCxoYfVIwn4c H48bqGhWT+CwH3IO3UxP7xKNrgbnLXouakx0EEsDWaGvqKHtC67yqoxTUl/LdTg48sOSodASWa1j T9PzolfTU+VNz1X1RPAjmMPxykDklaR9AnWie4WOznpTnkkCHsjrmqdapgyRT6igip53MJwpQGJK HbVi8JNRj02NSPSLjlcLCdGEUrhmdAmmD8rKppLBEaFNyL1Un9btcrN1k05eZzqpo3CAaWypj07W hXq4iTJU3QSIGCWATB7xgoWsBn+rHlRa9sZ/uXGdKefTp6KProREm2tDrX5gMOiYS04rA7OECAQj o5H2DchHlaACURgzStUMZB1CBKjQIbIeU7h6hCFPwJzKyWQJLrbikLbOSPQYUIKBm44WdOSMggsa 5NimIWQYGBOv6bnzFBvxJRN4I5ahUU0NZn8UzHtOMFFCX56nBnUK8ZqIvrFFevghl6Sz+uayBgm9 ZgyDBtJmbhFgTzjFjIje7+BJz0hxeUofQ7DG2yAN8EBIHuhvojz8GsIoa84BUbI3BTNlb7l6h2qp hX5OelNUk+0oQBai9hED5t6ztTAP2OwdXIwFowjiOdgjZLKGwZAx4IkGT2AE9HdYalurQQnDq13G 3ajrtNDspnG940A3oVrOmgJGP6u48TR6OlKPrqgQW09qPgQkgzqKesYQhSe1aWBBBTPPLk5HlMhj QifqTnqEURYNku9o+S10q58nw2BkWKiTYborwgcPUdD5SoKYw5NefviNE14g5x2fTnkl1OKqUJFp X2oDpzF46JtJfMkDY2fYBCBdz36YE1o8FA7v+F1HZD0t9AJ5pIfH1AtXc2kfH18RoUMRXHKaGqtC koCIMLRTu1uSViM2vK9107F60fMiFDsCWkdJaAlNaBM5k7pVOpEczXQERb4bEOUmyQP1g+Bq8kI9 a6zPSO2typ/2zqMBOunxVN+zbijnrcn/s9/B+9HNu4wukw7T83vgY4leR216hq8gJ/4atfvDR50V oYfjJs+Hns6gJBFVgRtDcz/dVzWKJjMN0kSp7aOZVJUoOTQ0olmVZ+oPqI2Ppspub13/bJ1Bn6Sn Q9mwh4Cwj5FLSU92osHUX9bp5oJsxmtzvNEANSo8RezQ/Yb8kzKlrN4jbgSraGQePxDmuSVojOLE CrbqcqyF+kQ33M8MjnyrBnSfziftK/be9tLAINTDRmkGi9gWSoF0uJRmQPdpDk/PLV0ADluCZoSP uh4XUjcHhaZDfZ1WaVz1Z+fmn1Pn7yNQsd+BySZ2NSZN8qCr9RwkpDaohYCPnntNzZW8J3LxyQgZ nCh4HlBw2PIuiFGXfgIiOJORn1oF4aYzyduqVr6OVC5bY2oDZj2VxtvnL2gYGjW195CwDuajxa1D VjXHMf5Re3SKznG9lGR9z38APZJtRfP3iQpCoySVLIgVSfqkwRrWpPUkEUM4OpB0U8/7SI0B+Tr8 4EavngjWk2WX6vTvXXwFaTs0RxKfHpr6BBhp4kZ234M/dqaDPfYew6mMbmW9BlLBpxw525OazIUK mUAMwGXcSr1kdNMkijrywPdTIurPhzdiWvRchqwxmoq1AL3kzqc1R8mLvNOA5VbS4814OjiU6g2I K5LTsbJeyUc9uAQdWCJ3tek64JOE16HKgdflpIHzg5Ib0WNY3yOKVNc7uV9HTx5wIx0Q1bM9Wc/H k5Gkh+6Dv/RIlGev0aZcB1UZBYKjUrHheXBqslFzbqVeNKSnZ7bRAEfPRMfyebhR7HGEcn0GMepr DOwD53T1RJYH/zQE08pG1gRqGjGlaRkh4ClH46Vk2ztGPG/qFYgU6CiEpoPoEZrO+WY39T0k7TJQ 2+9eSP6qZ0HgKBkWPfFC4QAz8GbCwqetVtnUDH3S3lpDTLaugXwg62AuXfIkGZRwdQKIFNPTtX6p DCEaOXboGQH2UsdrSmUHjRNBsFEzEtKIE8lwBCN+Kcbz6FgMX1LzdxCFxNQzQzrl0nSIpog0GaPx ZsLDW6mVYeqGkIWI0bcXFDKyeL/HBgBAzXNnjLaUERiNBY3tDY2N5ypAxL5WUJ9OD4xoUMoVZIPX oauMWEuaurohv1lNxEjT+SI5taY/kFbMGvzCzC/SCIaeI7//9SH3oedFEE0JA6MHcqm1jI9fOB+r OPG4EYhqIuyiJxFgSz1UEwkBQG16cqXBj3oYwelOUFpZruw9qvhhh0qNjyKhG350qFeu/7G5mN8D 3n99Iet/Q2gvHBSbZE6WTCokE6BG2mO0IGvizW6RVOx505PNcDxRa3r6CQEXvB6m7UJPQttSCipM gYKGmqiLADCR8abHWNWt8hnZoqN6PVzg9B8fhKa+Yqd6UBEgiOYDNGJPhlQVp5ralw3FZr80Bx7f I0ki+aP0R5kja6ZXS0r9dY3fLD0VyIrwj5qD1YNRUf85gE2JYB81YkcilKonJjUtU7UIrCiwOXfS WXLVVKkGZ5se2YcxUZRbUK4HTXW8KV7X1O3GcRfelUxImtaMmlVzSllji4KmdTp3MtS01DPRGmwU MEUJJZXYAdbxXWp7ck8oGw0WayrjprXca1/gSlFQjcgPNfXe0A/vULFvvq0IFAhh35OkS892Q6A6 Dz49awoBbmnBAVO6p4LZ0DCvBpkqea4BMMqcOtGRVVZ7RA+qDT0llvFLbKz+xwmKHGJPXNcl6RxI nIxPwEltOneJ1aLO+kMEVr8tCP8f07Hdu4f7X5ELM70dRvn/AAABhGlDQ1BJQ0MgcHJvZmlsZQAA eJx9kT1Iw0AcxV9TtUUqDnYQcchQRcGCqIijVqEIFUqt0KqDyaVf0KQhSXFxFFwLDn4sVh1cnHV1 cBUEwQ8QRycnRRcp8X9JoUWMB8f9eHfvcfcOEOplppod44CqWUYqHhMz2VUx8IogugCMYkRipj6X TCbgOb7u4ePrXZRneZ/7c/QoOZMBPpF4lumGRbxBPL1p6Zz3icOsKCnE58RjBl2Q+JHrsstvnAsO CzwzbKRT88RhYrHQxnIbs6KhEk8RRxRVo3wh47LCeYuzWq6y5j35C0M5bWWZ6zQHEccilpCECBlV lFCGhSitGikmUrQf8/APOP4kuWRylcDIsYAKVEiOH/wPfndr5icn3KRQDOh8se2PISCwCzRqtv19 bNuNE8D/DFxpLX+lDsx8kl5raZEjoHcbuLhuafIecLkD9D/pkiE5kp+mkM8D72f0TVmg7xboXnN7 a+7j9AFIU1eJG+DgEBguUPa6x7uD7b39e6bZ3w9cRnKeodbOXAAADRppVFh0WE1MOmNvbS5hZG9i ZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3pr YzlkIj8+Cjx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IlhNUCBD b3JlIDQuNC4wLUV4aXYyIj4KIDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcv MTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+CiAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9 IiIKICAgIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIgogICAg eG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2 ZW50IyIKICAgIHhtbG5zOkdJTVA9Imh0dHA6Ly93d3cuZ2ltcC5vcmcveG1wLyIKICAgIHhtbG5z OmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIKICAgIHhtbG5zOnRpZmY9Imh0 dHA6Ly9ucy5hZG9iZS5jb20vdGlmZi8xLjAvIgogICAgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRv YmUuY29tL3hhcC8xLjAvIgogICB4bXBNTTpEb2N1bWVudElEPSJnaW1wOmRvY2lkOmdpbXA6OWE2 NmQ3M2QtZjRhMi00ODM2LTg1ODAtZDg5MTlkZjJiNTVmIgogICB4bXBNTTpJbnN0YW5jZUlEPSJ4 bXAuaWlkOmQ1Mzg3MTc0LWM0YzctNDM4Yi1hYjkwLTgxYTZiY2Y2OTUwNSIKICAgeG1wTU06T3Jp Z2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOmVmNmNiNjdkLTZkOTktNGE0Ni1iOTUxLTU0MjkwMzNj ZmYxZSIKICAgR0lNUDpBUEk9IjIuMCIKICAgR0lNUDpQbGF0Zm9ybT0iTGludXgiCiAgIEdJTVA6 VGltZVN0YW1wPSIxNjU5NzQ2NTI0ODU3NTIzIgogICBHSU1QOlZlcnNpb249IjIuMTAuMzAiCiAg IGRjOkZvcm1hdD0iaW1hZ2UvcG5nIgogICB0aWZmOk9yaWVudGF0aW9uPSIxIgogICB4bXA6Q3Jl YXRvclRvb2w9IkdJTVAgMi4xMCI+CiAgIDx4bXBNTTpIaXN0b3J5PgogICAgPHJkZjpTZXE+CiAg ICAgPHJkZjpsaQogICAgICBzdEV2dDphY3Rpb249InNhdmVkIgogICAgICBzdEV2dDpjaGFuZ2Vk PSIvIgogICAgICBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjE2NmRhYWY4LTEzMDAtNDgyOC1i MjYyLTAwN2FjZTg5YTMyZCIKICAgICAgc3RFdnQ6c29mdHdhcmVBZ2VudD0iR2ltcCAyLjEwIChM aW51eCkiCiAgICAgIHN0RXZ0OndoZW49IjIwMjItMDgtMDVUMTc6NDI6MDQtMDc6MDAiLz4KICAg IDwvcmRmOlNlcT4KICAgPC94bXBNTTpIaXN0b3J5PgogIDwvcmRmOkRlc2NyaXB0aW9uPgogPC9y ZGY6UkRGPgo8L3g6eG1wbWV0YT4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAK ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg IAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAg ICAgICAgICAgICAgICAgIAo8P3hwYWNrZXQgZW5kPSJ3Ij8+vQWh3wAAAAZiS0dEAAAAAAAA+UO7 fwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAAd0SU1FB+YIBgAqBHHUThMAACAASURBVHja7V13eFTF Fz3zdtN7J430QkJJ6B2kVxGk2RACIqIoKtgL9oIKiIpSEhAR6UV6k44ESOghnYT0tpu6u9ndN78/ KD8h5b3dbM+e7+ND2VfmzZx33507954hMEMjiA6c3qYeJIAhTFuWYb3BEneGUE8K4gXAA4A7KCwA OIGAAWAJwO7e6bUA6gHIQVEDBjJQlIKgGKDFoCgFJYWEoZkMy2SmZPvfARax5l5vGYi5C1QgePQk y/pauygwgigCtj0lJJpQRAJoC8Bah02RgSALlKZQwiQTqkyyYJF04/baIvMomcmvEQQHz24rJMre lLI9CUEPUNL5nsU2VOSDktOU0ONQMifSc1anmEfRTH6+ZHcSEsUgSslQAjoUQKiRP1IRKA5Rgt2W NtYHb9z4pcY8ymbyP0B4+Gx3qlBMJMBkAP0ACE30UaUA/iEU2+REuDUra2WlmfytEIGB050tGDIe hEwhFINNmPBNvwgEfxMWf9i7CvdfurRSbia/iT9rZOCMYUoBmUsohgOwMn/4AQCFlGI1FTArMzJW 55nJb0KIjp5rL6+TPg+CeQAizFxvEgqA7mYZ8l1GRvw5M/mN2pePC4YCswG8AMDVzG2VcIal5JuM 7DV7AFAz+Y0EwcGz2wqp4n0QzAQg0NV9GYbA3cMJvj5uaOPjCjc3Bzg52cPZxQ4uzvZwcraDs/Pd /yek8W6XyeQQi2ogEtWgoqIaFeXVEItrUV5eheysItzOLkZNjUSX3ZnIUvJ5Rvaav83kN2BE+Mf5 sBbkAwI6C4CFNkkeEuqDiAg/hIX7IjTMB2HhvvDxcYWFhfbnzcXFImRnFSE7qwipqflITspAelo+ lEqtLvgeI8DbqVnxF83kNySf3m+mq9ySvkWAeRSw1fT1bW2t0L1HBGJiQxATG4IOHQNhb29jUH1Q WyvF5eQsJCdlIOlSOi5eSIdMpvEADgXBHyyhb2ZkJJSaya9XLGLCg+/MAegXAJw11iGEICq6Lfr2 i0bffu0R2zlEJxZdk6irk+H0qRv45+hlHP/nKioqqjV5+TJQ+m5adkC8secXGSX5I4Ond6CU+Y0S 9NLUNdt3CMTI0d0wcmRX+Pq5m8ynXalkcTk5E7t3/Yt9exJRVVWnqUtfoix9Of12wnkz+XUAP79J NraWDm8DeBcayLEJCPDExMn9MGp0d/j5mw7hm4JMJseRw8nYvu0Mzp25qYl5ghLAdxY21R/duLGl 3kx+LSE8OG4IKH4DQXBLJ6w9e7XDlKkDMHR4ZwgEDFoj8vPKsPHP49i08YQmvgbnlYQ8m5m5JsNM fg1i4MBFwoLc3A8AfAhAbaY6ONhg6tMD8dTTA03KrWkpqqrqsPmvE1i/7iiKikQtuZSEgLybmrVm mZn8mvDtA6cHsgKyEZT0VPca9vY2mDZ9CGbEDYOjk62Z7U1ALldg6+ZTWPHzXhQXi1rCqPUW1tWz jMENMljyh4fEjQfFGgAuZtLrdl6wYf0xrPh5T0vcoWMsIxufkbGhykx+FRAaOs+KsLVLCTBHnfMF AgZPP/MY5r02Dk7OdmY2q4mKimos/X4Htm45pdbEmABJ9ZQZlZ29uthMfh6I9pvpWm/BbiOEDFTn /C5dw/DRomcQ2c5f4227lXIHXm1c4OJi36pegpSbufjy87+QeD5VndOzCcXI1Oz4VDP5m3Nz7iai 7QUQqeq57h5OWPj2RIx7oleTeTPqoKCgAtu2nEJpSSVemDMS/v4erfZLsG9vIj7/ZCPKy1X2ZCoY Qsfeykw4ayZ/Y8QPjusLYAcAlcMwT07si3c/mAoHB82kHFBKcfZMCv784xj+PXcLbyyYgKeffUyj L5WxQiSqwacfb8C+vYmqnloDgslpmfH7zeT/DyKCZk6ihK4DoBJ7nZ3t8ekX0zB8RBeNtEOpZLFn 93ms/HUfMjIK0K17OL76Nq5VW/umcOhgEj75aD3KylT6CshByOS0zDU7zeQHEBYy41VCyVJV29Gn bxS++jYOXl4uLW6DQqHEls2nsOq3/cjPK4NQKMAbC57EjJnDwDBma98UxOIavPt2Ao4duazKafWE oZNTMxJ2tWryhwfPeB0g36vSBktLIRa8NRHTpg9psRtCKcWBfRex5PvtyMkpAQD4+bvjh6UvolNM sJndPPtwXcJhfPftNsjlCqP7AhD9ED/uDQDfq3KOm5sjflrxMjp3abmayJXLWfjskz9x7Wr2g38b NCQG3yyeCUdH85qAqrh6JRvzX/0V+XllRvUC6Jz8YSFxCwnFt6qcE9nOHyt+mwcfX7cWT9i+X7wN 27acAsverc4jhGDuK2PxyquPm92cFqCqsg6vzVuBs2du8neBKCalZsfv1lebBTolftDMBQRYrMo5 Q4bG4rfVr8HVzaFF997z93nMjluGpEvpoPeqUu3srLHkxzl46pmB5mhOC2FlbYGxj/dEZWUtrl7J 5sc9giddnbucrxAnZZm05Q8PinsBBCtVOefFl0Zh/hsTWmSRy8ursOjD9Th0MOmhf/fycsHKNa9p ZUGsteOvjSfw6cd/8F0ZrmIZ+lhGRkKSSZI/PHj6KIDZBZ7iUIQQvPfBVEybPqRF9z3/7y28+fpK lJY8LE4WFuaDlfGvw8fHLOqgLRw7egXz563gW05ZKgDbJyVrbbpJkT8yZFYXlrLHAfDKC2AYgk8+ m4bJU/urfU+lksXyZbvw24q9D3z7++jVux2W//KyxhbFzGgaZ07fxMsv/QRJnYwPFTPllPTRZS6Q VskfETAriArYswDa8HICBQw+/2o6JjzZR+171tRIsOD1Vfjn2JUGvw0Y2BHLf5kLKysLMzN1hGtX szFrxlKIxTw0cgn9lyX2AzMylst00TatTXiDg2c7EUZ5mIBf5ZVAwGDxDy9g3BPql+VmZhRi2jOL cTk5s8Fvw4Z3xo8/vwRLSzPxdQkvLxf07ReNQweTIJFwpfgTPwJ5QLkoeYfRkn/gwEXCGnHFPkJI d77EX/bTSxgxsqva97ycnIXp075DcSPVSKNGd8cPy16EUCgws1EP8PBwQq/e7bB3TyLq6zkXwzq5 OneurRAnnzVK8lsJgr4ByNN8j//si2kYO07tYi2cOX0Ts2ctRU11QzWz4SO74oels1ttra6hwNPT GdHRbbFv74UG87CGAQ8McXWJSaoQXU4zKp8/IijucUqwk++1574yFq+9/oTa9zu4/yLefH1Vo8vr vXq3w8o182FpKTSz7x5kMjmKikQoKqxAYWEFCvLLUVJSieqqOtTUSFBdfffPfyepEokM9ff6187W Gja2VrCxsYSjoy1sbCxha2sNN3dHeHo5w8Pd6e7fnk7w9HRusGK+d08iFr6xik8YtJplaM+MjISb RkH+0NDnQxhWcAmAE5/jJzzZB19+M0PtBabNf53Eoo/WN9qR7TsE4vcNC2FnZ90qSc6yFBnp+Ui9 lYe0tHxkZBQgPTUf+fllnJZXk3B2tkdQcBsEB7dBYJAXAoPa4EJiKn5fe4TP6akKCHtoayMNjZF/ 4MBFwoI7Oaf4Fpv37d8ev616VW0/fNvW03j/nbWgtOFABgW3wZ+b3oGrq0OrIbtMJseli+lIupSB 5ORMXEnORHW1xPgfjNB/CUtWg2WOpeaszjZI8ocHx30MYBGfY6Oi2uKPv95W2yofO3IZr8z9uVGL 7+hoi607PkBAoJfJEz7ndjFOnLiGUyeu40JiKo9oitEjG4QcJcA2b3//I8ePL1LonfxhgTN6EIac Bo8VXCdnO+zY9ZHa2jn3ozqNLZwwDMGKla9i4GMdTXb0C/LLceRIMg7su4hLF9PRilECYAtl6Xp1 JRNbTP6OXs/ZSewskgkQxnUswxCsTngDffpGqXWvtNQ8PDP1myYlNV6dPw4vz3vc5Ea5pkaCPX8n Ysumk7h+7bZ51t4Qlwgl36Rmr9kKFTbTaHEYRGpr8QMf4gPAvNfGqU38stJKzJqxtEniDxoSg5de HmtSI3or5Q7W/34U+/Ykoq5OZqZ40+hCCd0cHhyXSFn6Kt8vQYss/72wJq+StJ692iHh9zfVytBU Klk8/+xiXEhsPOzr4emEvfs/MxmdnnNnU7B65X6cPnXDTGvVoSSEfO3t77+Ia06gNvnvbQqRAsCT 61hHR1vs3vcJvL3Vy6L8+stNSFhzqMnfV6x8FYMGdzL6UTt98jqWLd3JNx/ejOZx1EphNela7gqR xt0euSU+4EN8APj402fVJv6BfRexNv5wk7+Pn9Db6Il/6WI6vl+8zWAnsJaWQtjYWsHRwRYWlkLY 2FjCxsYKlpZCODjYgDAEAoZ5sGONlZUF5AolWCULhVKJ2lopAEAsqoVYXAOxqAZicS2kUq1GpwZL hbKTISHPDc7MXF+iMct/L1szBTz2sh07rie+++EFtVp/O7sY4x//pEl/18vLBXsOfGq0dbeFhRX4 9qst2L/vQqPrFdqGlZUFfP3c4ePr9mAjPS9PZ7i4OsDF1R6urg5wd3fU2kJhTY0Ed3JLcedOGfLu lCI7uwg3b+QiI71Aky/GNQWE/RpbKFPL8lMB+wUf4vv4uOKjRc+o57gpWbyzcE2zE72PPnnGKImv VLJIiD+En5bt0npsXiBg4OfnjpBQbwSH+CA4pA1CQnzg5+8Od3dHvfaDvb0N2kW1Rbuotg36JzOj AInn03DhQirOnrmJqkq1RXM7WECxGZg0CtiibJHlDw+e3g1gznOdyzAE6/5YiO491Nv3efWqA1j8 9ZYmf+/aLRwb/nrb6IifnVWEd9+KR3IjadcthZ2dNSLb+SMqqi3aRd8lVWioj9HnNsnlCpw9cxMH 9l3EkcPJ6qlHUyxIy47/voXkjzsKYBDXcc9OG4QPP1bP6mdkFGDC4582WQJHCMHGLe8iNjbEaAaQ Ze9q3Cz9YYdGPukCAYPQMB/Edg5FbOcQxMSEoG2Ap8krUMjlCuzfewErf92H9PQCVU6VUSUTm56z OkUt8ocFzxxNQPdwHefgYIPD/3ytlqKxUsli6qQvm414DB3WGT+teNloBqygoAJvzv8NSZcyWkT2 Dh0D0bt3FLr1iECnmOBWm7QH3BXM2rP7PBZ99IcqG3OfTsuK7497C2EqfQ8J6Ht8jps9Z5TaUt6b /zrRLPEFAgZvLJhgNIN05vRNvDn/N4hENSqfGxLqjd69o9CrTzt07xFprjt+5Os/dlxPdOgYhLlz liMzo5DPaX0jQmYOT81cc0Al8keGzhrIsmxvruO8vV3VVl0Qi2uw9IfmRbxGj+2B4BBvo7BMK3/d h2VLdvLe3MHD0wm9+0Td/dM7Cp5ezmaWcyAwyAvr1i/EM1O/fiA7yTEuCwCoRn5Wyb7Nx0l67Y3x sLZWb5fQZUt2chY6z5g5zOAHRKFQ4oN312LHdu5KvOAQbwwZGoshQ2PRsVOQWTxLDXh4OmH12tfx xJhPHqwpNIPBoaEzOmdkJCTxIn94UFwnEAzn/Dq081e7AP1Wyh1s2nii2WO694hA1CNhMUNDba0U 8+b+jDOnGy9AYhiCDh2DMHRYZwwZGoug4DZm9moAbdt64p33puDD99dxHssoyXQA/MgPgrf5TI7n vDRa7WjDku+3c7oHT07qa9ADIKmT4YW4pQ1WagkhiIkNwePjemLosM7w8HQys1ULmDSlHzZu+Ac3 b+Zy8XkYr2hPu6CZAUpCM7hcpLZtPXHgyBdqFYpfv3YbE8d/3uwqp42tFc6eXwJbWyuD7Pj6egVe nvMTTp649pBLM3pMd4x9vEerKK4xBOzfewHzX/2V2/qzbBCn5VcS9kWAcB4368URaiskLPlhB+fy /uDBMUZBfEcnWzzxRG+MG98L7TsEmtmoYwwb0QWOTracK8KUMIObJXV09CRLuYTM5DPhGD9BPZW1 5KQMnD55nfO4xww0eY1lKRa+uQrV1RJ8891MjBjZVe0Jvxkth0DAoF+/9ti7J5GD/DSkWfIr6uwn g3Bnbk6fMVTtJfQ1qw7yeqD+/TsYZGffzi7Cy6+MRXiEn5l5BoKY2BBO8gOMPwdjyVyuG9nYWGLy lAFqNTLvThmOHeXe0ykqOsBgd1E3hjWH1gY/HvXhhNCmyR8ZPL0DC3DGLceO66k2MRPiD/JaADKm HB4zgOpqCaSSekhl9VDIlairk4FS+iAhraZGClbJok4ig1yuQH29AlJJPZRK9kGqQnVVHSi9GzpW KJWQSeVQKJSws7+b0uHoaIvQUB/06ReN0FCfh+7v4sqdXUApXJskPwtmOp8HffqZx9TqoLo6GXZs 4yfH2CnWvEGcNiGpk6G6WoK6Ohlqa6Worq5DXV096uqk9/7/roJbXZ0MtTXSe8dKHxxfUy1BbZ0M knv/r2v07d8en33x/IP9FqRSPnsCEGWj5B84cJGwIDeXU2sztnNog1xsvti/9wLvjnr0zTajcSiV LEQV1ai496e0tAqV4lpUVdVCLK596L+rKusgrqxFVWUtH/FYg8bpk9fx5LhP8dfW9xAQ4Imqyloe Z9GqRslfcCd3KHho6j/19EC1G7xl8ylexzEMQWBQ646Ri0Q1KC0Ro6hIhNLSShQVVkBUUYPy8mqU lVWiorwaIlENKiqq9VIRZgioqKjGqy//gu27PsL16znc1AdKmnJ7xnOdbG9vg2Fq7n6em1uC5CR+ 6b0eHs4mHTosK6tCQX753T8F5SgsrEBpaSWKi0QoKRGjpFhs9JZZV7iVcgfLluzgFTonBGmNkH8R A5o7huvkkaO7wcZGPVIeOnCJ97Et3YXRECZ/WZmFyMsrQ0F++YO/8/PLkJ9Xru0i7laH31bs43kk vdqA/KGBed0BcMbvxoztrnYDH90ZsTmoWxegL+TmluDMqRs4dzYF167dRkF+uZmRKsLWkoVQeNd9 s7dmcT9vQKYgKK3SSEkmFSgsTja4kkBAx3C5jY5OtujaLVytuxYViVTSpTGGaqWaGgl2bD+L3TvP tWrNHQFDseftbDAqCG/bWrEQEkAgoLCz4g57l9cIsGSPB3Yktig58FpKzsrCBuSnlHImzA8Y2FFt afEjh5JVmpQZcnp7pbgWq1cdwMYN/5iGHHgLoWQJBAzg6yrX2j3c7JX4dEoxbuVbISVfXcNIdgKP ZGoGBk53BtCZ69QhQ2LVbvy5szfvkZrAwfFuWZ5SweolPqz2ICtZbFh/DD8t341KcS3M+D9yyizh 6ybX6j0YQjGpVyU+3aoe+VmG3dWA/BYMMxgc+3QJBAz69ItWu+Fz543Fu+9PhVcbZ1hY/P/2Mpkc GekFuHbtNg7su4Dz/94Cy1KeCxa69enfemO1VqRHTAF3ynWz22WvMLWNztWMjITkBuQHyGAuhecO HQNbVEgdHR3Q6L9bWVkgun0AotsHYOpTA5CZUYgvP9+oVuG3tvDPsSt4c/5Ko/pKmSr523rI4eMi R4FIpfspKGUW4B7JH0nAp/24zu7dO0onDxcS6o3VCa/jifG9DWJQf193BC/P+clMfC63p1R3azJd Q1SaZ5VQYGp69uoHwq/Mf/19AnAyu1efKJ09HCEEz04bpPcBXRt/CF98upG3CkNrRm6Z7jb57tiW F/krCcU4YoHQ9Kz4bf/94YHbYylgeoGi2VIsoVCATjGtK8lsx/az+OqLTWZW84SG4vC8EOnLa8MO +2qZ8EhB9soGpV0PyE5YyulfRET4wcrKotUMZNKlDHzEQw3AjP+jSiJAvUI38ekIXxkYwhk2Fzja sO0a++EB+VmGdOO6SodOQa1mEKurJXhz/m/mvBoVQSlQVq0b629rycLPnXt8WND2jXoyDyw/RQwn +dUsyC4rq8K/51JwJTkLOTklEItr4OrqAG8fV/TqHYU+faMMbiX3q8//QkFBhZnNaqC8WggfF92E qNv5SpFb2rw3QsA2Tf52AbO9lVBw5g136Ki65S8uFmHr5lNIPJ+Gy8mZDRK5/vzjH9jYWmHqUwMw 64URcPfQv6bN1SvZ2L7tjJnFaqKsSqCze0V4y3DwsgOH5ScdmnR7WKGSUxrBxtYKoWGqF5V4ebng 5XmPY90fC/DvhaVY8uOL6PiI+ySpkyFhzSEMH/I+tvLM89cmvvlqs87y4jsHmV5aRHm1YU16CUVk k+SnLOVcso2ODlBbl+e/L9Co0d2xZfsH+H7p7Aa1vzU1Erz/7losfHM15HL9+NrJyZm4eCFNZ/eL G1SBEC/TSmuukjI6u1egB6++8x04cJGw8QkvAWf8Mrp9gEYbPWZsD+zY9THatm2ojLJ75znMnrms yc0ptInfE47o7F4udkr0iajFmC5VJkX+OpnuyN/GWc4n+VFYnHPHt3HLT8ApjxCkhVJCP393/Lnp HXh5uTT47eyZm3hz/kqdLizV1Ehw9Eiyzu43vnslLIUUE7qLIWRMp/xQUq878ltZULjZc3sJcsoG Nkp+QrnJ7+/voZXGe3g6YenyOY2mSB8+lITly3bprCOPHbmss6+NkKGY2kcMAHB3VGJU52rTsfz1 us1D9+YRWWIETGPknyQAwCnB4B/gqbXGd+4SijcWPNnob7+t2IvE86k66cSTPGo/NWf1qx7Ke39p WDkEJmL9pTq0/ADg48pjfkjZBn47Exlo5w+g2WwkgYB5oImiLUyPG9qo+hnLUrz/zlqdWORLOpro 2liymD304fLGtu71eLybafj+Eh1bfj5rChRMAzUShmUEnC6Pt4/rQ7n32oBAwOCVVx9v9Lfc3BL8 vla7E9GysiqdLWrNHV7e6IAtGFsKN3ul0ZNfJtex5XfmtvyEULeGbg+lnJEebfn7j2LkqG5NCr6u Xnmg2Q2pW4qc28U6ecZIXxmeHyBq9DdnWyUWPl5i9OTX9W6oznwMBkUj5Cfw5SR/W92Qn2EIJk/t 3+hvYnENtmw6qbV75+aWav35nGyV+GFaQbO+/diuVZjcW2zU5Cc6nrs42PAifwO/naEAZz6Bp6fu dgUcPaZ7k8XxO7ef1dp9K8XarRgTMhRLni9AAI9FmfcnlKBnmPHWBjM6vp+DNXc4vFG3hwCczNbl /q+urg7o1bvRDFTcvJmLjIwCrdxXmy6VkKH46pki9AirU+FFKUT7tsZZNcYYoOWnIK6N+PzEhfPi jrrVxu/Tt+lsi907z2nlntpKXXaxU2LFC/kYFataJMfRVomEuXcwuH2N0ZFf12oz9ta8FkLtGlp+ hnJafkcdk79rt7Amfzt8KNloSDC4fQ22LbiN3hHquTC2liyWzcjHx5OK4WxrPFEgRsd+j4MNL/KT Ll1mP5T7LKQULoZG/qjoANjaWjXqimRnFaG6WqJTV0wlq0eAoR2r8Vx/kUYyNgkBJvcSY0Snamw6 64SNZ1xQXCk0aPLbWeu21tnGgoWAAbgyYaRSoRUA+X98fm7L7+Rkp9OHEQgYhDShyU8pxY3rtw12 4Cm9W8dqJdSs32shpKhXElRJGBg6nGyUOjc4PMoZoVTWPLSdJ0NBHA1pwnsfgYFNp1Ncu3rboAc/ OdsGU5YGYNHmNpDIiEauN+H7QPxy0F2nSWNa9sF17mqxLB4qFxSCY3NpALC01H3RetuAprNIb6Xc MXgCUAps+dcJl7Kt8eP0AgSpkbOvZIHlB9yx5qgrWEpgLHC00f38hE/vEJnlQ2k8DL+B1H3Clbd3 01ORigrjyYDMKrbC1GUBOHpdNan1yjoBXlrth1VH3IyK+Pqy/IQHk6kVS1QmP9GDVLK9fdOulrGJ w9ZIGbyW4Iu/zvBbLCyvEeD5n/1x5pYdjBEOeohMMeDj81P5o+TnPEsflt++mXmGSGx8sW9Kgc+3 e+HPU80H18qqhYj7xR/phVYwVrRx0n0JKh/7bN0I+bkHDnogv33TUiZVlXVGSQpKgS93euJYEy6Q TE7w4ko/ZBQZL/EBwMtZD+TnY/mtWdUtvz4glzf96TRm1ThKgfc3eiO/ETXjr3d54la+cRPfxpKF i53u3R4+UbD6emuFyuRnWd2/H83l2qi747uhoErCYOEG74cmsoevOmDzWWcYO/Rh9eVKAgXL7ffY 10oakJ9zGVIfstx1dU3f08nIyQ8AV27bYPdFxweD991uD5gCdKXU9pDV56kW4douRPYw+QlEnJZK Dz52VZWkGfJrfodGfUS0Vh11BaXA3iRH5FWYhgCwPix/Hb/Ksdrjxxc9bPkJCzE3EXVP/rw7TReX eGuhnrilglzq4HaJJa7lWmPXBUetXF8fBfEhnroX4JJIeRgugspH/4mhhPCw/LqPq99phvztNSyg BQCMQD9pAydT7HH5tubTR5xtlZjUq1LnzxPqLdM9+flZ/kbID8pN/ird60neyWma/FFaIL+FUKAX 8h+7bq8VPfupfcSIG1gBXb/T+pBerOWRP9WYh8MQwof8urX8Umk90tLyGyephRDh4b4avydh9JNC oI09rFzslHi2vwi+bnKM66Y7629vzcLbWfcT3goewri0UcvPcrs9paW6/Xxeu3q7SaHamNhgrcio 6MvyS+WafekYQvHZ1KIHsfaFj5eolVSnlsvTRqaXTcN5qUIzaFBKxzCEFnKdl59XrtOHuXQxvcnf Ro/prpV76svn1yRsLVksfq4Qj0X/P/3D0YbF6jl5OqkHDmujH7VpUR2fsaMljU14b3OdVpBfpls/ +OjlxqMXAgbDRnTRUmTEuMnvaMPij1dzMSKmYcZrGyc5Nryai36R2nVfY/S01wCfbZAoi6KG5Fcw nOTPy9ed5b9zpxRXr2Q3+lvffu3h5qalsKDQuMlfJWHw/E/+je5SUl4jwNzVvjil5SzR2ED9kJ+P z8+ANCS/RFl5GxwpDpI6mc5y6Pf+ndhkFmlTglaagFAggLGjWirAm+t9Hkqdzi2zxJQfArWeHu1i p0Rbd/24PeU13GNHCdvAvWfy8rZIAHBq5OXnad/1kUrrseGPY437k+G+GDwkRmv3NnbL/2CQKfDF dk+cT7eFTE4wL94HhWLtF7zHBEr0Mtm9O+HlJj/LCBqS7FtWbAAAGYJJREFU/97fnK5PVmaR1h9i 08YTKClufMF5wVsTtZqCYOw+/0MDTQkW7/bAjkQnnaVHx+rJ36cUKK7kTg2xkAuKGiU/Ac3kOjk1 Vbt1sxJJPVb+tr/R3/r1b4+Bj3XU6v2FQgFMCSn51tieqLudLfW1sV5plRAy7nCx0ivIp7QJy89c 5Tpb20Xjy5bsQFkj6wmOTrb47Mvntd6Julal0wVu5ulmb2NHWyU6BuhHWrFAxCshsODRpLb/k5/g Cqflv5WntQc4dfI61iUcbjhDZwi+WTwL3t6uWu9EFxd7kyO/rqpP+0bU6m1XGZ7ZsI2GDxkAYBQC TvKXlVU1aplbihvXc/DGa781WjDz7vtTMWhwJ510orOzHcxQk/zt9CcoUMCH/JQ2Tf6UnJWF4BHx SdGw6/PvuRRMf+67BinThBC8ufBJTJs+RGed6GyCll8XYAhFn4g6wyY/IU2T/x44/f7kpEyNNLi2 Vorvvt2KuOd/aEB8GxtLfLN4JmbPGaXTTrSzs4atrZWZzSoiyl8GdweF3u6fL+IRxiWNuz0PziQU yZSgWVPb0p3J01Lz8Pfu89iy6SREoobyI527hOLLr2cgKLiNXjoyItIfyUkZZkargEHR+pWRuV3C IyuWZZsnPwT0DFiysLlrXL2SBYVCqVJYsLZWig3rj+HPP/5BYWFFo5Pa3n2i8Mxzg3Xm3zeFdlFm 8quKkbH6U8+T1DMo4rGAxwqEzZOfMhZnwCoompE9lEjqceN6DjrFBPNuoJ2dNWbPGYUJE/vi6pVs 5OeXobysCo6OtggM9EJM5xC4ujoYxic8qq2ZzSqgU6BEbykNAJBTasFHylHu5+dXlJHRDPnT0laW hYfE3QJFu+audOliukrkvw93d0e9W3Zuy28mvyoYFatfzdSsEh5zNIKMxmL8j054ARanuK515vQN kx3MdlFtTTLerw0whGJYR/2Sn4+/TyhuNvkMjxx6mutiiedT9aLjowsIBAwGDOxoZjYP9AiTwNNJ YfDkB6Up/MhvIT8GjvTm+noF/j2XYrKDOnhorJnZPDChR6Xe25BezE1+ljD8yJ+W9ns+eMT7Tx6/ ZrKD2q9/e9jYWJrZ3dz8zVGJoXp2eSQygswi7nGiDHha/ruH7+O64D/HrupFv1MXsLGxxMBBncwM bwYTe4hhIdDv+F/Ps4aSW5+TrasTpKpAfsJJ/uJiES4nZ5rs4D4/Y6iZ4U3NixjgyZ76d3lu5fPI WKW4XVCwso43+dOyqs8BqOC67r49iSY7wLGxIYiJDTYzvREMiKrRixjto0gt4BXmbNaFb8Tyb1Hy sf7791+EUsma7CBPnzHMzPRGMLWP2CDakcZr5xqiKvkBlmIz12XLSiub1dcxdgwb0QWBQV5mtv8H UX4y9InQ/35olPJb4KIgV1Qmv5Vt1UE+rs/2radNdqAFAgbvvj/VzPj/4IXB5QbRjqJKIa/9jSkj V538N25sqQewm9P12XdRL/LlusLAxzqaF73uIcirHkM6GsZGgLwWt4CajIygbJXJDwCEkE1cV5dK 67F/3wWTHvT3P3oKlpbCVk/+WYMqwBDDCG9fy+WWdCcU14BFrFrkT82sOgwgn+smm/48YdKDHhDg qfPCGkODt7MCoztXGUx7rvDYz4Al5DLXMc2I1WxRgmAd1wVu3MjB+X9vmfTgvzzvcXTvEdFqyf/S sDK9L2rdh4IlSMrmJj9D2bMtID9AFMxq8NitMX71QZMefIYh+Pb7WXBqhUXuQV71eKK74Vj98+m2 qJJwC4xRC+U/LSJ/as7qbADHuS5y4vg1ZGYUmjQJvL1d8dnn01od+d8cU6o3WZLGcOgqr5Tz1Ht5 auqT/675x3LOt4xSJMQfMnkiDB/ZFfNeG9dqiN8lWPKQ1r++UStjsD+JW6Wbghzj9UXnOiAts+0u AJxO/a4dZ7Wi62NoeOXVx/HUM4+Z/HMSAiwYW2JQbdqR6IRaHnvuMoRu4XM9HpXox6m7SwwLkNHN HXU/1aFvv2iTJ0a//u1x9Uo2cnNKTPYZR8ZU47kBIoNpj6SewYL1PtzkJyhIy6yeD9zk9NV4SRNX Sy3WAeDUKN+w/lijCg2mBoGAwY8/z0W37uEm+Xx2ViwWjjOsF3v9SReUVPJYb2Gx7W5+mkYsP1Bd fUnu6hzrQAgGcFn/6moJBg8x/WooCwshRo7ujhvXc0zuC/DGmFK9qrA9itxSCyz8wwcKltdm01Fu LrEXykXJWRqx/ADAMvKfAHAW7+7cfhbpafloDbCxscQvv83D0GGdTeaZwn1keKaf2GDao2QJ3v/L W5VdKx0A7AkLnNFDI5YfAESiq7VurjFtANLsdoiUUqSl5WPCk320upmEIblAw0d2RWFBhca1TPUx yf1xRoFB5Ovfx7e7PHHwisq6TkJCyBAbu9CVVVU3FS0mPwA4OndNEhA6B0CzmUUFBeXw9XNvNTo4 DEMwZGgsnJztcPb0zSb3FDN0TOhRiaf7Go7V33TWGcv3u6t7urOFwKqiXJR8TiPkF4uTal2dY224 fH8AuHQhHRMn92tVxeCdYoLRsVMw/jl2BfX1CqNqu5eTAsvj8mFlYRgv7t8XHfHxZi9QtMh78C8X Jf/SYp//Pixtrb8FDzlzsbgGi7/ZgtaGfv3bY+OW9xAQ4GlU7s4nU4rgaGMYlXl/nHLBexvb8JEi 5EJUdOD0Nhqx/ABQWnqh3s01tgrAWK5jb6XcQUxMMNoaERE0ATc3B4x/sg/y88qMYvI/uZcY0wwg pi+VEyza2garjrg1a/E9vZwR2yWUV5RNCebvCnFyjkYsPwCkZcavAgintCGlFB+8tw41NRK0Ntjb 2+CHZS/i8y+fN2jXz89VjoXjSvXejpt5VpiyJAA7zje/iV73HhHYsftj/LpyHpyd+eT5ECuNuT33 ec2AmQUeoc/Cwgp889VmtFZMmtIfW3d+iMh2/oY3UScUXzxVBFtL/bk71VIBFu/2xNSlAc1umyoQ MHjxpVFYu34B3N0dsX/vBYjF3HlHAqIo0pjbcx9loqRyN5dYIYCBnG/1jVxERbXV26YT+oarqwMm T+kPGxsrXLyQZjCqFzMHi/CknmQHJTKCTWdd8PpaH/ybbgvajH/v4+OKn399BRMn9wPDEFBK8c5b 8XxyyWqUjP3bFRWJja74tmhGERo6z4qhtclcsub3CbB77yfw8HRCa0ZOTgk+eHctEs+n6rUdnQIl +P2VOxDqOF05r8ICm886Y+u/Tqis47a9I0Z1xaefTXuoluLAvot4bd4KHv4J2ZSWvaZJFYIWT6dD A2f1ZBj2DB8Xqm+/aKyKfx0MQ1r1C8CyFJs3ncSPS3aivFz3hSKudkpsWZCDNk66WcyqkTI4fcsO ey454sRNO15RHG9vV7z/0VMNVs+VShZjRn6ErEzu+hGG0D63MhPOatztuY8KcVKeu0uMD0C6ch2b m1sKOztrdO4S2qrJTwhB+w6BmPLUAFBKceN6js5cIYZQLJlegCg/7crMF4qF2HvJET/ud8dn27yw /7IjbpdacsbthUIBZr4wAsuWv4SIyIbzpC2bT2L71jN8evlUalb8p80eoYkHDQ6e7SSE4joAP65j hUIBft+wEF26hsGMu8jPK8PSJTux9+/zWn8JXhhSjvmjyjR6zbp6BukFVkjJt8Ll2zZIum2D/HIL la/To2ckPv7kWYSEejf6e6W4FsOHvNfoZoYNvq6UPJ6RveZvrZMfAMKCZvYnhB7j8zXx8HTCjl0f t3r/v7H5wMpf92HXjnOQyzW/QixgKCb2rIS7gxLOtko42SvgYsvCxV4JJ1sl7K1YSOUE9cqGtBDX CCCuE0BUI0BJlRB5FRYorLBAdokl8kUWLdrtPSLSD/NeG4chQ2ObzQf7+IPf8ddGXmohl9Ky4ruB o/5co853RFDct5RgIZ9jO3cJxfo/31JpZ8eW4sTxqwiP8IO3t6tBvwQFBRVIWH0QO7afQXW16a6R hIX54JVXx2HYiC6c88ALiWmY9sy3vKTxWYrhGdncdbUaZV5gSLfj9VJ2BABfTp+wsAKVlbU6VURz drbDM1O/QUSkH3z93A2WFA4ONug/oAOemzYYfv7uKC4Wo7TEdEpEu3YLx1vvTMKHi55BWLgvZ/Zv XZ0Ms6YvQWUlL53Qk+nZ8R/y+hJqdJJTeIl1c+p0CoRMB0fmJwBcu5oNFxcHdOwUpJNOt7a2hFRa j3ffioejky06dTJsGXILCyGi2wdgylMD8NigTrCzs0ZJscgovwb29jaYMLEvvvpmBmbPGcWL9Pfx 0fu/89WGUjKEmVQmSuIlJaKVmGNEUNxkSrCJ19snYPDLb/Mw8DHdfAFkMjlGDvsA+XllGDe+FxZ9 +hxsba2MhkSUUly5nI0D+y/gxPFrvEJ++v6CjRzdDf0HdICVleqT4A3rj+HTRRv4Hv5LWlb8y3wP 1lrAPTwo7kcQzONzrK2tFf7c9I7O8v/3/H0eb85fCQAICPTCD0tno32HQKN0IYqLRTh3NgXnzqTg woU05OeV6aUdDEMQGNQGMTHBiO0citjOIQgJ9WnRms7FC2l4/tnvoFDwKskttVJYRVzLXSHSO/m7 dJltUS1S/AOgD5/jvbxc8NeWd+Hj66YT6zl75jKcPHHtgXsx/40nEDdrhNEvwFWKa3HzZi5u3shF SkoucnNKkJ9XhrIyzSym2dhYwsfXDT4+bggL90VomA/Cw30RGuoDGw1+QYuKRHhy3Ke8200opqRm x6uURKbVkQ4NneXHsGwiAG8+xwcEeOLPTe/A3UP7IdCysiqMG/3xQ53bu08Uvl4cBy8vF5OLrEgk 9cjLK0NpiRhVlXWoqqpDVXUdqqvqGqwtCAQM7O1t4OhkCwcHGzg42MLDwwne3q46kWysqZFg2jOL ceN6Dj9jBmxNz4qfpOp9tG7mwgJn9CAMOQ7Amtfx4b74Y+NbPNNVW4bTJ69jVtzSh8oOHRxssPCd SZg8pX+rqEE2xJd09sylquQ+FbMM7ZCRkaByXrbWg+wV4sv57s6d00Awkc/LVlFejQuJaRg1prvW dfHbBniirk6K5KT/7yxZX6/AP8eu4Pq12+jeIwL29jZmRhou8VlKmSczsuLV2hhaJytM5eLkm+6u nS0A9Of1KheJcCExDSNGdoWlpYVW29azVztcu3obOY9UBd2+XYxtW07D3cMJke38zV8BLUMqrcdL s39USe6eEHyclrUmQd176mx5tVyU/I+ba2wwAF47PBcWVuDc2RQMH9EF1tbaq4RiGILHBnXC0SPJ EFU8nDMik8lx9HAyLiSmIbZzKJxd7M0s1YZ3UFGNF2f9qFKaNwH9OzUr4GXgODV48gNAYHC3PfVS tisAXlltJcVi/HPsCoYN7wI7O2uttcvKygIDBnbEnt3nIZHUN/g9P68Mf/15HBKJDF27hUMgYMyM 1RBybhdj+nPfIeVmrgpn0RssUz+6omJ5i1JTdUr+wsJLrK9l+50KS8Fg8MgAvW8VTpy4isFDY7Xq fzs62aJjpyDs25PYaGYly1IkXcrAwQOXEBjo1eqK8rUVcIh7/geUFKugFURQoIDFoMzMtS3WiBTo +oGLa6/KPbw67KBKZgwADz7niCpqsH/vBfTsFQkPLYZBff3cEdnOHwf2X2wygUokqsHuneeQcvMO OnQKhJOTnZnFKkKpZPHrir348P11kEpVKKihKGcZweDMzFUZmmiHQB8PX1Z2RdLGqdN2lpAxAHhl mNXWSvH37vNoF9UWAYHa2xw6KKgNQkK8cfhQUrPKa9lZRdi08QSk0np0igmBhYV5x0Y+yM8rw5zZ P2Ln9rOqKttVA2R4etbqy5pqi0BfnVAqvlzj6Nxlq4DQUXy/AHK5Avv2JMLN3Umr6QihYT6IbOeP w4eSmi0uUSpZXLqYjh3bz8DdzRHhEX7mqFAz2LXjHF56cTlybqvssVSyDEakZ8Wf12R7BPrsDLE4 qdbdPnYnBBgLgFdeA6UUx49dQXl5NXr3idLa5DM42BudYkJw6FASFHIl51fp8KEknDp5HYFBbeCr gxQNY0JWZiHemL8S8WsOqiPjWEGAYemZ8YmabpdBmKkI/zgfaoHDAKJUOa9jpyD8+PNcrRan3LiR gzkv/KjSpGzQ4E6YM3cMOsUEt2rS19ZK8cvyv7E24TDf5LRHXHykMxRjU7PjtSJ1YTDf6A5tX3KR CWV7APRW5TwXF3t8v/RF9OkbpbW2FRZWYM4LP+KWihLkMbHBmDZ9KIaP6KLTijV9QyaTY9PGE/jt 130t2aftDMvQ8eqkLRiF2/NflFRelPpatt+ksBTEguc6AHB3ZXDP3+ehkCvRtVs4GEbzbpCDgw3G PdEL+XllSFNBe7OoSISDBy5h+9bTkMsVCA7xNmnV6vp6BTZuOI7X5q3AgX0XUVcnU+s6FDTe0qZm cmrqhmptttfgZmfR0ZMs5RKHtQCeUvXc8Ag/fPvdTK3WBezacQ4fffA7pNJ61S2NgEGPnpF4Ynxv DB+p3ZVrXaKstBI7tp/F+nVHUVzcIsHbegK8nZoVv1QX7TbU0ASJCJ75FgX9StU2WllZYP4b4zE9 bpjWcvNvXM/BwjdXtWjjbWdne4we2x2Pj+uFjp2CjK6OQKlkceb0DWzbehqHDya1XHKFIIUl9NmM jIQknZHMkDs4IihuMggSKGCr6rldu4Xjk8+fQ2ioj9b82iXfb8e6hMO8FAWag7u7IwY81hEDH+uE vv2iDbaskmUprlzJwr49idi3J1FTBTIUFKus6+RvXC1eX6vL5zF4cxMePL0bCLMTFCqzWCgU4PkZ Q/DKq+O0RqiLF9Lw8QfrkZFRoJHrWVoK0a17OHr0jERs51B06BCo0QopVSES1SDx31s4ceIajh+7 qml5xTsMS1+4dTvhoF7cC2P4xN4LhW5RNRJ0H15eLnjn/ckYNbq7VtqnUCixLuEIfl6+G7W1mpUB FAgYREb6I7ZLCNp3CEJwcBsEBnpppaJKKq1HWmo+btzIwc0bObhyOQtpqfna2GNMSikW18qEXxcU rNTbnqdG42gOHLhIWJCb+wGAD6HmvgLde0TgzYUTEROrnfh7cbEIPyzejt27zrXYFeIzZwgM8kRA oBc8PJzh7GwHJyc7ODnZwsnZDg4Otv9xV9gHcif19QqUl1ehuFiMirIqlJZVorSkEoWFFSguEulC M3QPhHgtLS0+S9+cMrq1+IiQmSMopesAqJ1W2btPFBa8PRHR0QFaaWNmRiF+XLYTB/dfMtqdGbWA swA+TsuKP2IwURVj7MV7btAfAB5T9xoMQzByVDfMe22c1jbNuHolGyt+2YPjx65o/UtgwDhNKfNp evbqw4bWMCPOwlrEhIXkvkwovgJg15KXoGevdpg2fQgeG9RJKy3NzS3B+nVHsWXTyUaLZUwQ9QDd Qwh+Ts1MOGaojTT6FMTIwOmBLMOsAjCkpdeKjg7A8zOGYtSYblpJUS4vr8LWzaewbesZ5NwuNkXS 3yQgvysZNl6baQlm8j/yHOEhcS+AYjEAx5ZezMPTCROe7INxT/RuUiu+JaCU4tLFdGzbchr791+E RM00AANhUAFhsZWlbEL67bWXjavpJoQI/zgfKsTHIJgF9XeafAhhYT4YN743JkzsAzc3R423WSqt x7mzKTiw7yKOHb2Mqqo6Y+hqKQj+Zlmy3i/Af//x44sUxvnemiAiguO6UmAZ1FwXaAwWFkL06x+N QYNjMGBgR3h6OWu83QqFEv+eu4VjRy/j/LlbGls40yAuEZD1xFL+x61bv5cbvbtgunOuRUxYSO40 QvEleMol8u40QhAV1fZeSkJHdOiondycsrIqXDifivPnU3HhfCqysgr1EzUi2MIS5bsZGesyTYkh Jl9z19HrOTuJvcVcQrEQPMslVYWLiz1iO4cgJjbkbkpCxyCtpC5L6mS4desOUm7euSdGm4PMjEK1 MkxVmqMAE9Oz4reZGjdaTcFpR6/n7GR2lrMo6DsAtLobtkDAoF1UW8TGhqBL1zBEtQ+Av7+H1jI3 S4rFyMsrw53cUty5U4o7uaUoLhZBJKqBqKIGYnENZDL1tx1lGWWoqVn9VkX++4iOnmuvkErmUkrm a9odag42NpYIDfNFRKQfQkK84efvAT9/d/j4uGpclLe2VgpRRQ1KS8UoK6tCUZEI6an5uHY1G6mp eaqmMKSmZcVHmiIXWq3UQHT0JEtFneM4SuhsaGCNoKWTaVdXB7i5OTyQRBQIGNjZN65SR1n6IFeH UoqqqjrU1EhRVVmLqkYkx1vk8hC8mp4Zv9xMfhNFRHBcV0oxDwRTAFiZe+T/HpUCwvCsrJWVZvKb OEJDZ3gwSmYKAX2aEvRs5f1DQTA6LTN+v6k+oJn8TSA8PC6YKsnTBPRpULRrhdx/Ly0r4StTfkIz +Xl9EWbGCigdAxYjKUF3GJDqhRYgJ6CvpWYlrDD1cTWTX0VERk5zY+UWQ8HSkSAYDsDLhB4vERSz 07Ljr7SGsTSTv4X9Fxr6QpRAqexJCXoBtCdA2kFDeUU6AgvgBAX9IT0rYS+AVlN4YCa/hhEcPNuJ oYoeDKHRIAgGJSEAggEEwjAiSSKAFhDgCgtyipFjd+qd+ILWOFZm8usMi5iQkBxfCyIIYSkNAEvd CeBFCTwAuFNK7QkhDhRwJIANHi7QsQNwP19CCuBekB8iAKAEdQQQE1ARJURMWNz9G6ighC0CJfks oyyUSusK8vK2SMxjcRf/A18b/P13IK5VAAAAAElFTkSuQmCC "
         id="image108997-6"
         x="979.82825"
         y="24.463888" />
      <g
         id="g109577-0"
         transform="matrix(0.32075687,0,0,0.32075687,973.68532,69.606897)">
        <path
           d="m 79.797436,18.8 c -0.5,-1.6 -1.7,-2.9 -3.2,-3.7 l -30.5,-14.6 c -0.8,-0.4 -1.7,-0.5 -2.5,-0.5 -0.8,0 -1.7,0 -2.5,0.2 l -30.5,14.7 c -1.5000002,0.7 -2.6000002,2 -3.0000002,3.7 L 0.09743584,51.5 c -0.3,1.7 0.1,3.4 1.09999996,4.8 L 22.297436,82.4 c 1.2,1.2 2.9,2 4.6,2.1 h 33.6 c 1.8,0.2 3.5,-0.6 4.6,-2.1 l 21.1,-26.1 c 1,-1.4 1.4,-3.1 1.2,-4.8 z"
           id="path10349-4"
           style="fill:#326de6" />
        <path
           d="m 75.097436,50.2 v 0 c -0.1,0 -0.2,0 -0.2,-0.1 0,-0.1 -0.2,-0.1 -0.4,-0.1 -0.4,-0.1 -0.8,-0.1 -1.2,-0.1 -0.2,0 -0.4,0 -0.6,-0.1 h -0.1 c -1.1,-0.1 -2.3,-0.3 -3.4,-0.6 -0.3,-0.1 -0.6,-0.4 -0.7,-0.7 0.1,0 0,0 0,0 v 0 l -0.8,-0.2 c 0.4,-2.9 0.2,-5.9 -0.4,-8.8 -0.7,-2.9 -1.9,-5.7 -3.5,-8.2 l 0.6,-0.6 v 0 -0.1 c 0,-0.3 0.1,-0.7 0.3,-0.9 0.9,-0.8 1.8,-1.4 2.8,-2 v 0 c 0.2,-0.1 0.4,-0.2 0.6,-0.3 0.4,-0.2 0.7,-0.4 1.1,-0.6 0.1,-0.1 0.2,-0.1 0.3,-0.2 0.1,-0.1 0,-0.1 0,-0.2 v 0 c 0.9,-0.7 1.1,-1.9 0.4,-2.8 -0.3,-0.4 -0.9,-0.7 -1.4,-0.7 -0.5,0 -1,0.2 -1.4,0.5 v 0 l -0.1,0.1 c -0.1,0.1 -0.2,0.2 -0.3,0.2 -0.3,0.3 -0.6,0.6 -0.8,0.9 -0.1,0.2 -0.3,0.3 -0.4,0.4 v 0 c -0.7,0.8 -1.6,1.6 -2.5,2.2 -0.2,0.1 -0.4,0.2 -0.6,0.2 -0.1,0 -0.3,0 -0.4,-0.1 h -0.1 l -0.8,0.5 c -0.8,-0.8 -1.7,-1.6 -2.5,-2.4 -3.7,-2.9 -8.3,-4.7 -13,-5.2 l -0.1,-0.8 v 0 0.1 c -0.3,-0.2 -0.4,-0.5 -0.5,-0.8 0,-1.1 0,-2.2 0.2,-3.4 v -0.1 c 0,-0.2 0.1,-0.4 0.1,-0.6 0.1,-0.4 0.1,-0.8 0.2,-1.2 v -0.6 0 c 0.1,-1 -0.7,-2 -1.7,-2.1 -0.6,-0.1 -1.2,0.2 -1.7,0.7 -0.4,0.4 -0.6,0.9 -0.6,1.4 v 0 0.5 c 0,0.4 0.1,0.8 0.2,1.2 0.1,0.2 0.1,0.4 0.1,0.6 v 0.1 c 0.2,1.1 0.2,2.2 0.2,3.4 -0.1,0.3 -0.2,0.6 -0.5,0.8 v 0.2 0 l -0.1,0.8 c -1.1,0.1 -2.2,0.3 -3.4,0.5 -4.7,1 -9,3.5 -12.3,7 l -0.6,-0.4 h -0.1 c -0.1,0 -0.2,0.1 -0.4,0.1 -0.2,0 -0.4,-0.1 -0.6,-0.2 -0.9,-0.7 -1.8,-1.5 -2.5,-2.3 v 0 c -0.1,-0.2 -0.3,-0.3 -0.4,-0.4 -0.3,-0.3 -0.5,-0.6 -0.8,-0.9 -0.1,-0.1 -0.2,-0.1 -0.3,-0.2 -0.1,-0.1 -0.1,-0.1 -0.1,-0.1 v 0 c -0.4,-0.3 -0.9,-0.5 -1.4,-0.5 -0.6,0 -1.1,0.2 -1.4,0.7 -0.6,0.9 -0.4,2.1 0.4,2.8 v 0 c 0.1,0 0.1,0.1 0.1,0.1 0,0 0.2,0.2 0.3,0.2 0.3,0.2 0.7,0.4 1.1,0.6 0.2,0.1 0.4,0.2 0.6,0.3 v 0 c 1,0.6 2,1.2 2.8,2 0.2,0.2 0.4,0.6 0.3,0.9 v -0.1 0 l 0.6,0.6 c -0.1,0.2 -0.2,0.3 -0.3,0.5 -3.1,4.9 -4.4,10.7 -3.5,16.4 l -0.8,0.2 v 0 c 0,0.1 -0.1,0.1 -0.1,0.1 -0.1,0.3 -0.4,0.5 -0.7,0.7 -1.1,0.3 -2.2,0.5 -3.4,0.6 v 0 c -0.2,0 -0.4,0 -0.6,0.1 -0.4,0 -0.8,0.1 -1.2,0.1 -0.1,0 -0.2,0.1 -0.4,0.1 -0.1,0 -0.1,0 -0.2,0.1 v 0 c -1.1,0.2 -1.8,1.2 -1.6,2.3 0,0 0,0 0,0 0.2,0.9 1.1,1.5 2,1.4 0.2,0 0.3,0 0.5,-0.1 v 0 c 0.1,0 0.1,0 0.1,-0.1 0,-0.1 0.3,0 0.4,0 0.4,-0.1 0.8,-0.3 1.1,-0.4 0.2,-0.1 0.4,-0.2 0.6,-0.2 h 0.1 c 1.1,-0.4 2.1,-0.7 3.3,-0.9 h 0.1 c 0.3,0 0.6,0.1 0.8,0.3 0.1,0 0.1,0.1 0.1,0.1 v 0 l 0.9,-0.1 c 1.5,4.6 4.3,8.7 8.2,11.7 0.9,0.7 1.7,1.3 2.7,1.8 l -0.5,0.7 v 0 c 0,0.1 0.1,0.1 0.1,0.1 0.2,0.3 0.2,0.7 0.1,1 -0.4,1 -1,2 -1.6,2.9 v 0.1 c -0.1,0.2 -0.2,0.3 -0.4,0.5 -0.2,0.2 -0.4,0.6 -0.7,1 -0.1,0.1 -0.1,0.2 -0.2,0.3 0,0 0,0.1 -0.1,0.1 v 0 c -0.5,1 -0.1,2.2 0.8,2.7 0.2,0.1 0.5,0.2 0.7,0.2 0.8,0 1.5,-0.5 1.9,-1.2 v 0 c 0,0 0,-0.1 0.1,-0.1 0,-0.1 0.1,-0.2 0.2,-0.3 0.1,-0.4 0.3,-0.7 0.4,-1.1 l 0.2,-0.6 v 0 c 0.3,-1.1 0.8,-2.1 1.3,-3.1 0.2,-0.3 0.5,-0.5 0.8,-0.6 0.1,0 0.1,0 0.1,-0.1 v 0 l 0.4,-0.8 c 2.8,1.1 5.7,1.6 8.7,1.6 1.8,0 3.6,-0.2 5.4,-0.7 1.1,-0.2 2.2,-0.6 3.2,-0.9 l 0.4,0.7 v 0 c 0.1,0 0.1,0 0.1,0.1 0.3,0.1 0.6,0.3 0.8,0.6 0.5,1 1,2 1.3,3.1 v 0.1 l 0.2,0.6 c 0.1,0.4 0.2,0.8 0.4,1.1 0.1,0.1 0.1,0.2 0.2,0.3 0,0 0,0.1 0.1,0.1 v 0 c 0.4,0.7 1.1,1.2 1.9,1.2 0.3,0 0.5,-0.1 0.8,-0.2 0.4,-0.2 0.8,-0.6 0.9,-1.1 0.1,-0.5 0.1,-1 -0.1,-1.5 v 0 c 0,-0.1 -0.1,-0.1 -0.1,-0.1 0,-0.1 -0.1,-0.2 -0.2,-0.3 -0.2,-0.4 -0.4,-0.7 -0.7,-1 -0.1,-0.2 -0.2,-0.3 -0.4,-0.5 v -0.2 c -0.7,-0.9 -1.2,-1.9 -1.6,-2.9 -0.1,-0.3 -0.1,-0.7 0.1,-1 0,-0.1 0.1,-0.1 0.1,-0.1 v 0 l -0.3,-0.8 c 5.1,-3.1 9,-7.9 10.8,-13.6 l 0.8,0.1 v 0 c 0.1,0 0.1,-0.1 0.1,-0.1 0.2,-0.2 0.5,-0.3 0.8,-0.3 h 0.1 c 1.1,0.2 2.2,0.5 3.2,0.9 h 0.1 c 0.2,0.1 0.4,0.2 0.6,0.2 0.4,0.2 0.7,0.4 1.1,0.5 0.1,0 0.2,0.1 0.4,0.1 0.1,0 0.1,0 0.2,0.1 v 0 c 0.2,0.1 0.3,0.1 0.5,0.1 0.9,0 1.7,-0.6 2,-1.4 -0.1,-1.1 -0.9,-1.9 -1.8,-2.1 z m -28.9,-3.1 -2.7,1.3 -2.7,-1.3 -0.7,-2.9 1.9,-2.4 h 3 l 1.9,2.4 z m 16.3,-6.5 c 0.5,2.1 0.6,4.2 0.4,6.3 l -9.5,-2.7 v 0 c -0.9,-0.2 -1.4,-1.1 -1.2,-2 0.1,-0.3 0.2,-0.5 0.4,-0.7 l 7.5,-6.8 c 1.1,1.8 1.9,3.8 2.4,5.9 z m -5.4,-9.6 -8.2,5.8 c -0.7,0.4 -1.7,0.3 -2.2,-0.4 -0.2,-0.2 -0.3,-0.4 -0.3,-0.7 l -0.6,-10.1 c 4.4,0.5 8.3,2.4 11.3,5.4 z m -18.1,-5.1 2,-0.4 -0.5,10 v 0 c 0,0.9 -0.8,1.6 -1.7,1.6 -0.3,0 -0.5,-0.1 -0.8,-0.2 l -8.3,-5.9 c 2.6,-2.5 5.8,-4.3 9.3,-5.1 z m -12.2,8.8 7.4,6.6 v 0 c 0.7,0.6 0.8,1.6 0.2,2.3 -0.2,0.3 -0.4,0.4 -0.8,0.5 l -9.7,2.8 c -0.3,-4.2 0.7,-8.5 2.9,-12.2 z m -1.7,16.9 9.9,-1.7 c 0.8,0 1.6,0.5 1.7,1.3 0.1,0.3 0.1,0.7 -0.1,1 v 0 l -3.8,9.2 c -3.5,-2.3 -6.3,-5.8 -7.7,-9.8 z m 22.7,12.4 c -1.4,0.3 -2.8,0.5 -4.3,0.5 -2.1,0 -4.3,-0.4 -6.3,-1 l 4.9,-8.9 c 0.5,-0.6 1.3,-0.8 2,-0.4 0.3,0.2 0.5,0.4 0.8,0.7 v 0 l 4.8,8.7 c -0.6,0.1 -1.2,0.2 -1.9,0.4 z m 12.2,-8.7 c -1.5,2.4 -3.6,4.5 -6,6 l -3.9,-9.4 c -0.2,-0.8 0.2,-1.6 0.9,-1.9 0.3,-0.1 0.6,-0.2 0.9,-0.2 l 10,1.7 c -0.5,1.4 -1.1,2.7 -1.9,3.8 z"
           id="path10351-8"
           style="fill:#ffffff" />
      </g>
      <path
         d="m 979.70513,104.65119 v 22.43663 h -22.43659 v -22.43663 z m -11.21829,5.1777 c -2.00459,0 -3.7127,1.26935 -4.36437,3.04811 v 0 l -0.18016,3.2e-4 c -2.25604,0 -4.08493,1.8289 -4.08493,4.08493 0,2.25604 1.82889,4.08494 4.08493,4.08494 v 0 h 9.08909 c 2.25604,0 4.0849,-1.8289 4.0849,-4.08494 0,-2.25603 -1.82886,-4.08493 -4.0849,-4.08493 v 0 l -0.18015,-3.2e-4 c -0.65169,-1.77876 -2.35979,-3.04811 -4.36437,-3.04811 z"
         fill="#4285f4"
         id="path119683-8"
         style="fill-rule:evenodd;stroke:none;stroke-width:1.12183" />
      <text
         xml:space="preserve"
         transform="matrix(0.49503935,0,0,0.49503935,1045.5149,-116.23423)"
         id="text4916-5-1-21-4"
         style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-7-9-7-3);fill:#000000;fill-opacity:1;stroke:none"><tspan
           x="40.033203"
           y="354.32031"
           id="tspan212343">Sandboxed containers</tspan></text>
      <path
         style="fill:none;stroke:#000000;stroke-width:2.80652;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1"
         d="M 928.26759,-324.95557 V 769.61549"
         id="path167533"
         sodipodi:nodetypes="cc" />
      <path
         style="fill:none;stroke:#000000;stroke-width:2.80652;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1"
         d="M 530.81693,232.82076 H 1328.437"
         id="path167756" />
    </g>
    <ellipse
       style="fill:#ffa700;fill-opacity:0.190454;stroke:#000000;stroke-width:0.818378;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-69"
       cx="273.86151"
       cy="130.0182"
       rx="25.765219"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.24009357,0,0,0.26458333,219.51006,-1.1191351)"
       id="text22680-7"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-9);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="190.22717"
         y="488.62894"
         id="tspan212347"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212345">Load 
</tspan></tspan><tspan
         x="168.47584"
         y="521.96231"
         id="tspan212351"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212349">balancer</tspan></tspan></text>
    <ellipse
       style="fill:#39c681;fill-opacity:0.210933;stroke:#000000;stroke-width:0.818378;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-0-8"
       cx="328.01706"
       cy="129.87068"
       rx="25.765219"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.24009357,0,0,0.26458333,273.66563,-1.2666451)"
       id="text22680-6-2"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-36-6);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="163.78182"
         y="488.62894"
         id="tspan212355"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212353">Static file </tspan></tspan><tspan
         x="177.31048"
         y="521.96231"
         id="tspan212359"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212357">serving</tspan></tspan></text>
    <ellipse
       style="fill:#fff8ba;fill-opacity:0.753457;stroke:#000000;stroke-width:0.818378;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-6-9"
       cx="381.31787"
       cy="130.39983"
       rx="25.765219"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.24009357,0,0,0.26458333,326.9664,2.9666863)"
       id="text22680-1-9"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-2-93);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="158.59301"
         y="488.62894"
         id="tspan212363"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212361"> Database</tspan></tspan></text>
    <rect
       style="fill:#000000;fill-opacity:0.02;fill-rule:evenodd;stroke:#000000;stroke-width:1.29494;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="rect117-1-7"
       width="194.16092"
       height="155.75706"
       x="6.0630865"
       y="387.93649" />
    <rect
       style="fill:#2080ff;fill-opacity:0.0866484;fill-rule:evenodd;stroke:#000000;stroke-width:1.12535;stroke-miterlimit:4;stroke-dasharray:1.12535, 2.2507;stroke-dashoffset:0;stroke-opacity:1"
       id="rect117-6-5-8"
       width="177.61057"
       height="128.59392"
       x="14.354407"
       y="398.67398" />
    <rect
       style="fill:#262362;fill-opacity:0.119759;stroke:#000000;stroke-width:0.761158;stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:0.761158, 1.52233;stroke-dashoffset:0;stroke-opacity:1"
       id="rect31353-6-5"
       width="166.26421"
       height="61.33704"
       x="18.684372"
       y="447.25122" />
    <ellipse
       style="fill:#fe0000;fill-opacity:0.213973;stroke:#000000;stroke-width:1;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path15701-5-3"
       cx="72.767502"
       cy="355.95572"
       rx="46.290901"
       ry="15.684086" />
    <ellipse
       style="fill:#e6fae0;fill-opacity:1;stroke:#000000;stroke-width:0.803215;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-8-3"
       cx="49.493484"
       cy="474.48599"
       rx="24.766687"
       ry="13.185097" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,3.7500598,220.88641)"
       id="text22680-10-2-8"
       style="font-style:normal;font-weight:normal;font-size:42.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-6-19-6);fill:#000000;fill-opacity:1;stroke:none"
       x="171.41656"
       y="0"><tspan
         x="176.29519"
         y="502.78518"
         id="tspan212367"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212365">Outside </tspan></tspan><tspan
         x="199.94104"
         y="556.11856"
         id="tspan212371"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212369">world</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,-18.20407,271.99833)"
       id="text45097-6-3"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-3-0);fill:#000000;fill-opacity:1;stroke:none"
       x="42.939453"
       y="0"><tspan
         x="218.94336"
         y="758.01953"
         id="tspan212375"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212373">Web 
</tspan></tspan><tspan
         x="208.26367"
         y="798.01953"
         id="tspan212379"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212377">server</tspan></tspan></text>
    <ellipse
       style="fill:#cdf8ff;fill-opacity:0.681529;stroke:#000000;stroke-width:0.786137;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-7-02-7"
       cx="102.20284"
       cy="473.95682"
       rx="23.709324"
       ry="13.193636" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,33.561628,271.11229)"
       id="text45097-9-7-9"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-8-80-7);fill:#000000;fill-opacity:1;stroke:none"
       x="42.939453"
       y="0"><tspan
         x="228.51367"
         y="758.01953"
         id="tspan212383"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212381">API 
</tspan></tspan><tspan
         x="208.26367"
         y="798.01953"
         id="tspan212387"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212385">server</tspan></tspan></text>
    <ellipse
       style="fill:#f1dff7;fill-opacity:1;stroke:#000000;stroke-width:0.778699;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-7-0-6-3"
       cx="155.16939"
       cy="473.99545"
       rx="23.256241"
       ry="13.197355" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,86.984908,271.15089)"
       id="text45097-9-2-1-7"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-8-2-5-0);fill:#000000;fill-opacity:1;stroke:none"
       x="104.11133"
       y="0"><tspan
         x="220.70898"
         y="758.01953"
         id="tspan212391"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212389">Data
</tspan></tspan><tspan
         x="195.43555"
         y="798.01953"
         id="tspan212395"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212393">pipeline</tspan></tspan></text>
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-6-1)"
       d="m 48.853378,436.40117 0.64,24.89988"
       id="path80708-3-8"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-6-1)"
       d="m 155.16929,460.79825 1.14045,-24.01546"
       id="path80708-7-2-7"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-6-1)"
       d="m 102.20273,460.76335 54.10701,-23.98056"
       id="path80708-7-1-1-4"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-6-1)"
       d="M 49.493378,461.30105 156.30974,436.78279"
       id="path80708-7-1-3-5-1"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-40-1)"
       d="m 48.853378,436.40117 53.349352,24.36218"
       id="path80708-7-1-3-4-99-9"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-7-46-7)"
       d="M 72.767502,371.6398 48.853378,410.08686"
       id="path80708-7-1-3-4-4-1-0"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-7-5-6-2)"
       d="m 72.767502,371.6398 30.241428,38.29954"
       id="path80708-7-1-3-4-4-3-4-9"
       sodipodi:nodetypes="cc" />
    <ellipse
       style="fill:#ffa700;fill-opacity:0.190454;stroke:#000000;stroke-width:0.818378;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-69-8"
       cx="48.853378"
       cy="423.24402"
       rx="25.765219"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.24009357,0,0,0.26458333,-5.4980702,292.10667)"
       id="text22680-7-8"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-9-7);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="190.22717"
         y="488.62894"
         id="tspan212399"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212397">Load 
</tspan></tspan><tspan
         x="168.47584"
         y="521.96231"
         id="tspan212403"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212401">balancer</tspan></tspan></text>
    <ellipse
       style="fill:#39c681;fill-opacity:0.210933;stroke:#000000;stroke-width:0.818378;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-0-8-5"
       cx="103.00893"
       cy="423.0965"
       rx="25.765219"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.24009357,0,0,0.26458333,48.657498,291.95916)"
       id="text22680-6-2-8"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-36-6-5);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="163.78182"
         y="488.62894"
         id="tspan212407"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212405">Static file </tspan></tspan><tspan
         x="177.31048"
         y="521.96231"
         id="tspan212411"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212409">serving</tspan></tspan></text>
    <ellipse
       style="fill:#fff8ba;fill-opacity:0.753457;stroke:#000000;stroke-width:0.818378;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-6-9-4"
       cx="156.30977"
       cy="423.62564"
       rx="25.765219"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.24009357,0,0,0.26458333,101.95827,296.19249)"
       id="text22680-1-9-3"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-2-93-9);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="158.59301"
         y="488.62894"
         id="tspan212415"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212413"> Database</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,100.49945,445.16446)"
       id="text4916-7-1"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-8-5);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="40.033203"
         y="354.32031"
         id="tspan212417">Your cloud stack</tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,78.213468,428.37332)"
       id="text4916-5-9-3"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-7-5-2);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="40.033203"
         y="354.32031"
         id="tspan212419">Kubernetes Cluster</tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,-1.7269602,304.76074)"
       id="text62482-3-8"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect62484-0-0);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="33.445312"
         y="978.39258"
         id="tspan212421">Security:        ★★★★☆
</tspan><tspan
         x="33.445312"
         y="1028.3926"
         id="tspan212423">Performance: ★★★★☆
</tspan></text>
    <image
       width="19.167257"
       height="20.873243"
       preserveAspectRatio="none"
       xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAL8AAADQCAYAAABWSLCJAAApKHpUWHRSYXcgcHJvZmlsZSB0eXBl IGV4aWYAAHjapZxZklw5b4XfuQovgfOwHIJDhHfg5fs7zJS6W9L/0LYUUpWqsm7yEsAZQFy58z// fd1/8Wvk2l0urddRq+dXHnnEySfdf37N93fw+f39ftn9fi/88+tuf7/uI19KfEyff/b6ff2Pr4ef F/h8mHxW/nahvr7fsH9+Y+Tv9fsvF/q+UdKKIp98V+LG90Ipfr4RvheYn9vydfT2j1s7n48/76R/ /jj9ZT++Wr4v/uXfubF7u/A+KcaTQvL8ndJ3AUl/okuTT/r7u/DCkBqfZ76lr/xYCRvyp336+Wuw oqul5j++6B9ROfbnaP34zP0arRy/L0m/bHL9+fGPX3eh/Dkqb+v/9s65fz+L//z6PDF9VvTL7uvP vbvfd8/cxcyVra7fm/pxK+8zXscNZ12oO5ZWfeNP4RLt/R787mT1IhW2X974vcIIkXDdkMMOM9xw 3scVFkvM8bjY+CTGFdP7Yk8tjriS4pf1O9zY0kibyMa0XtiJ6c+1hPe2wy/33q3zzjvw0hi4WFBe /Nvf7t/+wL0qhRD8d/NJC9YVozabZShy+puXEZFwv5ta3gb/+P3rL8U1EcGiXVaJDDbWPpewEv5C gvQCnXhh4eOnBkPb3wuwRbx1YTEhEQGiFlIJNfgWYwuBjewEaLL0mHI0IhBKiZtFxpxSJTY96q35 kRbeS2OJfNnxdcCMSJRUqbNOhCbByrmQPy13cmiWVHIppZZWehll1lRzLbXWVgWKs6WWXSutttZ6 G2321HMvvfbWex99jjgSoFlGHW30McacvOfkypOfnrxgTouWLFtxVq1Zt2FzkT4rr7LqaquvseaO O23wY9fddt9jzxMOqXTyKaeedvoZZ15S7SZ38y233nb7HXf+jFr4lu2vv/9F1MI3avFFSi9sP6PG V1v7cYkgOCmKGQGLLgci3hQCEjoqZr6HnKMip5j5EamKEllkUcx2UMSIYD4hlht+xM7FT0QVuf9X 3FzL/4hb/L9Gzil0/zJyv8ftT1HboqH1IvapQm2qT1Qf3z99xj5Fdr99dHEeVjR2XMesj915h0q+ wJD3gGwlTUuetay8eyQSZZ0U9yp1rVCL38YKSiNq12drsc7FRgJoY9fKp4n177BA+hHzaWn1ckap JwGXtd/EBoZ1WWezMe1Wc+xhaJYml9/eFvvWiA8/Xs3CTumEdLuJ8/ywyOJL2ueEzi7kuS+x0BcW wNZz7jUSIbC2zplvY1tKLG3ePUCxYhf+S63NlYu/k5cks1v2LnN7XpBRDfc4s0K86r1kZGl39JTt WMijEvzSLyIg2r4E0QNFvNkh8OO0uwmPQtD77KdcB1xPAnNtbNJ3Z32T9Xt9jCXoqiWTFa0Hi72W OBcBFVIUn/Yo7MrZyU/HZRustZu1zRv7NHwc1fJunt3Ze0S/eP+w+jzVeC945sRhgOMM/QSb+exT kgt7tfve/9guzSrlsBFLFg43NqmURY20Wvgp38qcJZ4xWgqHAvCQ6KK6qk03t0SL3R1ru5H1Gtuz D0l0VmfX8xns5s2H5OrxtLgo85iDEJvsIii5pT2zSwTpkEFs/GVRVCS6BpIGs1cnVMSFtIrsE/We WHAyaU9l9Kmhs4tF2qy4H5/8p4/ppD7zGFZXVRSNVN9nk0vcbZjsF8Es9bpt22axLG5pBG1uEhJh pUK5MEzNqd5ZyXne/65zQZ/FdoVbpykZAQZqfHJrIxRSI2fK7Uaqm8RISuxtd4VyfZ17bijp2sq5 LVLtRm7RVoR2be9F2k4D/EkNEiDVACyMCi6lRW7dRvmWfM3vQWX5WyjpxlfInhZJ+xMGS/ZEvCFb tgtnQX02jULhFshbMJqfA1woicZ7ANul7hAXyF1B32rUDdlAvhmgyP6AlcdRGq0kMFiCyKjy3T8l TdYdUoKkqj2Rfwe4KkDnIOuH1g8nWKZUQB4AyZFAIFILg6ohTws7UQ0cY4u82Z4NiN9r6ofJkNIX So78a4IT6z3GvcFpbq2FBBwtQBxwZ5Gef2oTNpU7MgW+alnKmjjzb8jY2QqweN7pXSVdp++HYjcQ olNAgZLbbbDdvR3PdwLYAVSB9cB2POQ5KH2EVPn0dOO8uTneaSZ4op0AXxJHNubCKABXL3sMEm/n tSs3B4XximXgRwuCj1OinU2Nx35c68DIKKFQqR2MwJOwGq61E6vZcBYFw8sB57NGg84AFba4GFxA 9O+gguqObhWSAgKqVFxi/QAe+D59yYOFAvQAJUGv7OGBEBawjTehKHcXxFVYaYDV0ZWx71PBBxcE lcF12kHAYlLUm9I4fYP7rZjKhrxIZKbeY8Zx0WKQR4ehXSCCYxSWk3cyuP8Tkexf1Rb/10d0GznB 7aVYeUcKcwAsA1UMPy43X64CtDaxA71CkMXAVUzVxEWmUyPAQcGDGBci4pYgOCOdD7gFLKHEgQhz Al1ysV1eF6A3YC1RF16QdPepYslwZgb/Zqyd7KPMJqUMsICRs4Iw0NZxsDdwCKSwVMXpAiYIiOf1 pAkymev31Y1zvbW5cNlWCOYs0AEEDHoAE46Uwx9QwLfWtGZGdlwwOfASXMAAktgI1AWktIwkA0gy iZRjGbzXBrq1hNwd6gNFRE22S961AUTPuWytCSeye0gpBMv1xLMCWHuSDiQIeoxyva2m3QtojYgg 7CeSqBN6hEu20NOPcDAn5OhtpHNkyx7Ps0+NS7MY4Tvc1RIiZ/NtcwQkD7i0T6tn2ViWzwLkM1BK LVKGETzseCyypfSDX1J5A0cgtaHxZ4FvWnPnsBu9wXKp7gQxwV/rIENIs42j7oUAkw0VcRZt9Ppb fvGxhOV2WlwhID8A78r77TLAdnUzhiinnEee+IteIKSYQdO1jW1lixGkq0toVO/KZkvJC7A8Izu4 rn4wI/KkKCbEP0k+NDIVEwqS795AUaFu7qPczD/Y7OtgaCLakGx+rLBjBMPB6xTgCQLChnFD0Ho3 UsQm5GCWL5bp5IyWm7wyw+XLsT/HKEpSjsixrjH77QhLdEDmUgi7Cl/HASHwGSBKPZxdzoI/Vov6 G5wfru7Ky0haIPGg01PBXKyLUV7USl4DVLyFYIG4laRmt1DZCVQlo21HWGQYis5Nn8T9pt0wFkNV +JXOsUZipR84USQnu5CJwsrgGLxiqtpD1ksFJKdNW0Y1EFgpiF3x3qOxZnWOWBjymhQDlgvif5Gl VNfAZaABCAnAjPKeJbs7w0SLjwV4tn+iUZFcoQaHSuJKDrRABUF4a+zhAwagUv7onIQ+QkBTMlR8 kUJGO1K5SMgMgakVADVgAGJlr1+nIUtZoNoooZWBsQ8fU7uukGpAPJCXOkmHABYyoUmm4gUFYKyS IUJYjSeGIP9McfF+FPRmH8XQa2/HW7QNI4BgQ0ATiop4sUNcskPK3vrOs0Vv5BiGOd1VoelGhNjj xn3a2gNPC4Tjral4dCZ3165hdSUzJrrHK01Bksi/bvsqzSZ6D2nFxCpRT8GbH46MRSsFAMgIxm0U CmproGAH5gQwoyTUbqgwPf6uiXElGpZeemRbB3HF9x+PlmLjIdrr+UKVzkJN+TYGe5MGwPlwSg6l YrtIWvb0LBgSgJZEo9brcMi1bU/3pBC7fKOgBjIjgZ/i9H9TnHzED5VbIKHt8S/nNu4GqxgdMhdZ FKifJYPnCWhlu1BpYaRNwSb2XTGWGEf5l8GWn6w7g2myXB35s70T+wLgRzVr3D6iH8kgPk0kOjAh ykWY6Pa6cgh6b4MwQ8ixoLqhzJjGcdAlSYjuwLhBgyyurEcD6OJKxSEbQSKkBcAJV8L9Sqx7VNOX Ak9rY2YbeZR4g7XuRMQsg4ba5d8ErSmLu9C9sysslFKy1QJUN/DplOXFzyxAmlzArnMRD1mgGngJ KAS8b93GPOhDVGEvasFwT3hqLgAZYiEEryYkghHIkYOlc+RoRYXMCcMow0GKmcFAwOCI0oratYAD ygngy5U8QyFAHSg3q3gfwGOTJC4ONCb7mXACJB1sxcK8wXZk4I0t8+/CDVxUCMKfJUI3mMCMaYXs 7MKesPhwiFg4kh9jc9FTUl+Sl+wkAMC1F76uHbZ1AXcmkQ5hqR2B46+kLU6SVZ3gsrxZShvUe64N xxwBH0yAZys61I3hQN8ATBnaE2ZTQuAK2IdbhgvRh4OoJdReUgxAMfYQMe1x/hhoyGLxzrAT5Msa MAbIZrhhVZh7asdBnxATyLiuAWxk6sD4y3cahlS4AtnZQU0q2hQp1BC3WpRc7WQ8Izke/FJUb0Q5 YVwxNVAZih4sByQh5d4xACNTowDx0Y4etX9YGvS62akqDcTu14DSJQtFm7gEB517kL3y2sR9BMlZ zAuJ7KWwgEOkgJA5YW4Rp9zAZt99aPi3CrxREAspKLu+kMM5e/w4EhF9sfDAZ0jhTJjaI51V38Dn wP1V8BfoWFumG1fGW3gSb7uVoRYiH3WN6yl5UGLVISEZSMCBxZKV66Gq2xU7ogqNpFJP46KQwPpw mjnBPCQK4/NCKgvDQYqSy9w5cpWUH7qdUtQRDQ9coPd6l/ZBnSd5jrYTCEnEKADUAmpRwabGSOWj KyGQwAH0CjgRFECkVIisHnBGeu5PU6Ogt65jp0eOSNMD/IAooVsyWQbU4QK2KWM4BDnJGyHQuXus KlWAcQwN3FkLqMddOrIC/MG+cWlACAZqpAxMQ2bKRgUpbVVGqzfhNQM7c0GSgGM00trAtoyid8Bf JP+fjkPo++fx2NGIDJahwQU09SHgEFbMqg0bSXagwuFkDAGIc4OZQ6Gjz9Hh0B+eAVMcO6kfd8Jb E0g2S63nqM6dyhykuHV7UDUu0WjCK/L2CC2MMTcDrRDlkrC9ZRJPtJEyOKr+Cc0RTaN0cF0TlpLg 6Q3eJ2igEZuG9Mu1E4ona9UpQP+chDqHPZAFltEQOMipxqMZtxERUohOTDbcf0WQoaH+irvnGhZq QNToiJXYUxiCF++0BfjUCdsIL+JrlGyb2uIuN3eFTiw4hiSVaw6ul00nM0oYyAeYJ8140GFZO3fU zIJ3cQ61CUU9m9QhDzCIHEAzo9SQVovN1gLrGgoOyE20GjsT2qFUC9R/hloDYMNWzgJbWRdiY7E2 FB2B3dLCbvPSDdyUqu/oxhdSLD8m8VRwljsryaAVnCssAj29vTkD2UPMwueVjoC/nth7g/YuA0zp mzDOkw9Lq5dW/SxowQ+HbNmYuhk2VadQT8d7wgNwKpli+GOYBTUwKAdgnXo6Jn7skCEOkTig0gUo YNnnbcBc4jPhfhKYZF+kD2WtOGCB12ow1ohaQWkI4mbqaKtTB6U9pwt2ywmjJ9A42D9HGm3xa/YK MeXjxyaPNvuIGLU8UGLkmMovYDrUM6k4JXAqSBjug6QzAMmBlCgH2YyiT7h3EhOiCBAXthbiYgk4 vg2KoooaZJxxJjAnVi2wT1gmI6ccFg/Ske4iemyNSAdEf6Kqjb71a2RV46oUG2lKAe6kji9oHs+h mtjBhKpVE3BgK6kLBAKqAWvTSEmcAvqHeIJvwxpbMUX/2Fm1ZKrkNW4GcG1yIQ5BvBAtHgKgUA7+ MRc2h5JUv6kO5NDAxyQqGqFiJxA+JAb2v3mMWwsXlQ3Pu4KKNXEMts2iahGyHY1IDAAopeEhDxb8 6RYuwA0Ir+vrSHP3JVVgLrqI/YF2Z/QHqAqIJXQUhI/SRbD0pWMTgDc2lTbZBKbX26F49btvxpRX 5O/oDobv4LHqGCtAUNjKaJRZHqTP86B4j4FQRrQgLXF55B/CTxpv8UPoisFdONGZytLYqafsSUt+ AFAlTbAR5AosSAbzBer9KVG1rXCHOuFkX4ZOrSeUzc8F3AeWvwtaVqQwSMaKd44FAQfREbaI0bxs PrmOGEVuk7z83edWf1jHGRhZ/wPyf0J/RhaTmCQNdwnUkl8QCEyd1dvDq3f/evF5ItkF5dWhcGUQ 0A0ScmqZePAeroO8ok5pJD0oYL5aiTUrJObQM0hOHp0lwsICFBdQ5Qme2QvUIWQ4jdLlcltl79Bs OjWCvAPwQ/WDTxCeSUUWNuD4D+CA2Q1XDmJGIrsApaXfAXAND9rQxmQcwrgfNtNTDpgQlIDp4Gki l5u0V0QDwbQdxY13mUAQthau7uoRRgkt9aupGgAJrMGPyI5nnPQ9OjFVpwKEQqd5pB/gBup4rSXJ e6jvr5h6ZO481EbC7bamYx51MUF7hCh7AkHark3UBPGO7Y5OqxDebCGwhM4GNxHQauyX/imNw65i 2yB52RbtqKSMpd7UBWABO6oRZTp5qE0nE2y8byxXpx8RIIyyLiXiUTw0b2ywjn2vdg3HuUuOZPfz d9VL1sAc3bcKSFDqGNoMGcfD7e6kbhBek7yEYEbCKUtGH6or51hVMuwp1pALSY6js0EpsdWimDZ1 oBBnQJE7ruQ7VVX4FAPIlyLs/Y5rGnoORoWdsdfUGjYHj0H+E8tBRFHIU70AMAX46ve1CPBPr/Pz 6pE3wbp1eKPArIbMidGZOvM44SOdNXRGyMqJVQF48KIZ01l1yowWsYsFmAfZgiElUuWNl6DS0NvX wZz8cEZcCkCUfmk0QnyIWkQJjdc2z89eSPaypWeiTngjj2EAn183BX3k2djXvZY4BbXRXdAFojFT UCQ7OT4VcTUUyL8DL3sSUtoWMNGpaKQYBi6bGAq9pMh0pgaBNUSezr8xPFnnGF9okPMWrGA8x/Sk aFRXGt0AvCZMDf5iAThcLWCvPCCDraAudEYCeXisZGpRDlMLs9dx0KwHOHsVWtn3eRzmnfCvqD4b zAKBy5Jm0ol3ACMHcoz3GC8bIV2WHdU8xocEP2X/eOOL0EJsH3VPUEeQDSpLh15xXNUBmuJDFg2m ++2kC08BQcPflOtFsHuYdLB9QOfpqQMVHgMwJ+sksxCuzy7ID6AvcbW8qlq56iCjzXBEmDQqAQep pT17KKxoa8LLqNnXG5MwE/yhDSlutlndOZ1nQB9oTvW2dKqJuCtUvzrNE394BeTcLrmC0U5P2p9u ePNWF5oG79HQj1cnA6UhKe4YSy4oSBjCIlh5EFbNYfTTM/4e9CNbQfUE4aGi7iG7dWYOoRW1wIKQ aqJQmwp+h5oQ7GMOncY2rDq3IpFaDanOaljkw+QbI2rkkQQimOuzTnRvU48+If4RG90pv6/OxCQ2 5R2R97OBvDphhoTV8CIcRNUIVFRjRZ2IKSeu1g2CABicFaj1cssNj1rJQ7IVNJUUoVyg1ammaqpA yDk6+78XyQBwnJ5l5PCtQGAfMVO0HUEjNi6YAeQ5CK0etJA4q7XHTwIHsHKhpNGfhJm/MxZzj86O Tfa4jMe08ugVxlM3H5RUxmSMGH/vBvrqjL08Xh1PU+uI/X3ElbOvpvNujxjtCqV2NSP4WEHAWUGl C3x55GdiD0Q72arOIXr7as6iaUrHf1oBJB0iYqtd0FJVjSWwQ0YppEMI1aCGTWW1l0yN6CSEvN55 Gmg6o47TTlIDoLgaBoSrZhcpGcCujkNtISIgzXTkBx7DwIS66xD54nnBJ0pcB6bDqnz0rSW4rfYK DPWacVQI2aUOWWBxuB6SG0F+NdvR7C04dcKJGqAm7bXOT6cm93a9IF9Oa7lz6dpzizqKVpMBfU19 A8GKLlyXv2ZIQ2gQSQbdcCOQ5m5E2iFCEI0DMsiU58cjn/rz9CPvqq4iBrpgLfOnsYw7qKQ0tQTH fPIQGIm4rSQnPi08A0VsfE8fK6XenI861Snq0kk0eMTH4D7zUYpMtUJLtexAwP0BrzXGQW8jq2Gy Wzw7gkjPT/ck4Q/vQOE03C7Qw1cQvNsoRna9XIcWw6fgwgJesm0l+glLLT4N6+FKM2Q3ravXRgYi znVIC6W0z7EUmcOya3Avy0ELCL+Uc8mmoJ4LPE1JDcAYSWQwT3n4pFYYuZGRKCRgl4/AtuE/zE3p AsQsdv7iN9iroJXV54gRwXj4SDU1+/SQ1YzIOq6QsEE7IxJQSATdgeo691IZaSDN2DidDyZU0hoo gk7WZR0PRSUSUIIeEnujAjHZWe2cCM1cN6jkZqzE2EhITocOOqNPsPhW18w8FJeS3zOjyY4GFDQk Q/x00I67ByA0E0FpUQGas6u7RhQlNRk36C62fFxPrdlMoUU1nDEb345M48W4H8Qb7926OhGCUE2r CBcKlonAIkfTUh/5aIzi4QnbsyTlQojfvNUQ39WBHL484NcyYckR4kNkZI+WRnpBTgFlJ2Fhw7pO 8TwFl1UzuFKKA122hNVz2ERhTo1WmU6RALZl+40EoFxGADGqRjbQI+yqcf/4jjY0psE+k7oZjFns Aixa5UWyiBaZSwJdnZSXiSbSyV7POpxcEONtyXQadSURCLFaIVkthB1xupqX6IeEPJSvgH6od575 IbiYIkX9kdTrew4r1zWmhgUyrErM53l7O9WvbdxxdWNR+gfPQsI+7XVz1lEODl4IeUzHIYRdqqB0 Ag4u653I4hu7ziVwCQTcaeSnBtRMYEfYR6loAA4WhlI0ooHuoop4A9wH1yGOOCBvhX+1L9povMWd roKHiLImmdC8XWGv8vpibP6hlueRptVY4UjqrOEcSLJDCmFExlCfVP0jsohS4I8akwAMSCkEkkGL mlJI4jqNRwe1RXRS2Opv4sD9UAcAMCoacA+oOkxJl9eOUeCmDqTOQXWCTXx9oeDDa6YSCAiiDOSB K28s4SaZv4HX7FA+wquHnsnto341noi0rVv3H+rbL4lLqSlSHfZmV4vbGujheyi4uTQL0kGJx1E4 e+oada6BkIZ91UywAEmN/cEuSy5Db4A96tSVrnEJndNEonh6fzmDn0bXNBBToTU18+EzjCHWegTN Ecn0HCzt2Ozs5EIaLaMmLr5fDfMAp8bry9obzRGAD/Cuqov0LMY7f1HXNL+OTYMBfR0qKbdFcxa9 QghyzRE0fAi+xSTVnNhrpKdX4zjoR45aM0nHmZ18sjfAoTx2aOm69/7hKbJv4PsNSPurTuKOQQSO sdhIRiIifiA4pHqmJoDDqmmuxB5BEhQFJdm1jUEn2URlSnvwDfv0FKyG3wbKkKNRHUZ8BW7c6cvx 04IYmj7hPUyTAyQojK5R+qlOW9Hwm843QXsUcjq4jmctVtJpxSkONRxUl5ARUG6aD7l8hBW7GqyJ fFqyKDWKHI8u+JFZfXEj6pez7TCT01Al97i/p9gUfgEcvNzlc0tK9gTsDGG9Tg0Od6XD4UOme6l+ y6/nT8pbO1lZg6QfFLI6FbUC4riA9dli4FwNuMeto+Be3hmBalGTI1JVhc0OGyBWjwe+VJ8DZNqv aShzptYg5bfkHkKPjTtBaqotSx1Ir4d80Al4kRB02KjGOkaMwm0E7wxglSsBHvEG9n6COXjBqcZi yvWodMp6E4hDowPJDwdbod7MgyoFZE8SRPDt8fqax0UVrhzgO+QeNnwBFfKhEtkadwi8E3zVzRVJ rMJWQhJHJ1wZoYIClMHzahYdG7yZpv/QLZ8kIvfWr5No7pT0pksgXPTTAsneSFCYVKmeGTiSOIL5 Z5bB/I7I7EsDWmUjeTQcIjXr9lSTp0oWYqE1c8MuYTJQtdzsNNiX1NnI1621wvJqLEENSKTJu8SS LcGcDi4d/WPzTv/Bxv4PE1rk4ZKkzRs9q5HDoyggiJd4pWmK9YGDVpuqRkz69rw9oKdxPdXp0bDc Dio9VDH0J2ihiNPqlPD3IMpJuXgwmchvnfwfLxgDMPDgZA2bib9Slwdzw+1+HKrmE8+PxVYMyNCM Flao14Ok1LC6RMip+cHT8UAG1BA0tKcJCXTqxolBbbH4J43ZXvwSjnNB2cbaudJoETWss/uJ7NOg lVYaB7ilARfR8lyid58199OA/ApCZx3hH7yIhIJuG2jVPKkGu6bpnAuOJLdAIizHuGqVyFcgUWUZ KG0y15L63BtD17wD9wOeSj1T5D+WO4u7MKxq2c0qIyGpD6mbz910Fus1drNhigAJS5DkMr0rmoyp 6tJHNawQMBRrBig7cLmyCAqSL08Jo001uhrQnTq2P2gjBNAMddXj2L2iWWcYmjtYSD6d8i/5ILbu qvmsczP02+cUYJbfh3iV4w4hb2XDt1GkpREbsSjeRzXN5hb0vNrAR6egXNpXjb/yhx+SKNMzAbjs gc5GWdV6sxwquzEIGOvmW2z2G+6nzEzV78G5qOG1Bb/BT5dKlDX1GzdQ3DrcEMkOWqE6NAQpk85r QnwTatZiwWIsWB1CgLKPerul97+XPwyLO5osepITqJbxhu1B59fiJydDQv2BfZF46vmHN3lEMCi7 TZoF2xqrk5l3kkvUjqaz0QMPMPY7OYU2xse5BoQj3nN/3kYz3IA0oPoeCIBZNGV4HEWZQEAKK6tt AICMXif6BFPFmmCCj7JMOCY4EcSEBwkqMcMI6HAKDQ7cuvqGIwFlil/j/ev1roCWqyPJRpkNUvPu FJDBqK5DmiESJJlU04D6M3NEDdNIGsvRo4MhvSgRqIOsxfXBAKqFjPcmswK+oC2oTsoRGbUWdRr7 Nl7tNDKqURHN/oemg4ItcSw7w+7WVg/SiTWjyXWaD6I9CQmEst9vIHtMLPfl1oY64rGehkQfWABS Jya+vNBwYLLUJ2Kr6ykc5CJ1U8X4QzYAjyrL6CFMh5j8UgMQpXOP9pnsQfmhLcRlL4ig90Cbxzcw gcRIQ2dU3DBWHoHJrQ0ShjxL/nX0NjwjUQaaw0w6/OEWkUnariMXIdf+OTFmX2peXse4G/iRzkZT ZhkNDDTC7xxdAV4xdPfIlBDXtiFxkjVkd6YO8KFKXDfJOlPndkdxRCgujZvBJhq0peiQ4SRDjBp6 B/vMoxnhnie4RsY2YNU07TxhbLB+4H5fQ3Mh3a2qrwahB7lzcNU06QrhGn5ds1/QmWbWhFsaLIJD Eyn1Oe3y4l4Q0rfehdAYmB4/x4TedLCCE1SvMWMrAvdUqIo3n/w9YhrkJ0mdPE58Y/wwYQk7w0Um lCbMiUBGR1XCxhqfC1fjspqoF7iOofM1zYW+riYpAernNh07SC2rpUjtJxaFzxwaKQlA2xDz4xmn JuwjDCG7rlFu9bDtII11lKWRPA0x4x9zCxnvj1VtGvxTcI+SRWp46nmYAYaGGjWJnMkehB+IWnS2 eDXFyEvcNHwUCSI6eNNX0BdKSToBQDsjVDZ1CSi5bN5LZ4tIkjeTSiTeCJbOYp0O7qvmzH3YmnRr B9O9gCbNK8JNqHUNbJKWW32uGzQrfZK0etTBvahdLT2HEA64mqIOa4Fm2PHXn0Ur9aQ5B2pnmRw8 NbRY2oar5+eZIyA2sMNwBA5S/VOCisfQYEk1TLqpl9SlqSq+Xs9afEaYe1dLS0/awCxoYfVIwn4c H48bqGhWT+CwH3IO3UxP7xKNrgbnLXouakx0EEsDWaGvqKHtC67yqoxTUl/LdTg48sOSodASWa1j T9PzolfTU+VNz1X1RPAjmMPxykDklaR9AnWie4WOznpTnkkCHsjrmqdapgyRT6igip53MJwpQGJK HbVi8JNRj02NSPSLjlcLCdGEUrhmdAmmD8rKppLBEaFNyL1Un9btcrN1k05eZzqpo3CAaWypj07W hXq4iTJU3QSIGCWATB7xgoWsBn+rHlRa9sZ/uXGdKefTp6KProREm2tDrX5gMOiYS04rA7OECAQj o5H2DchHlaACURgzStUMZB1CBKjQIbIeU7h6hCFPwJzKyWQJLrbikLbOSPQYUIKBm44WdOSMggsa 5NimIWQYGBOv6bnzFBvxJRN4I5ahUU0NZn8UzHtOMFFCX56nBnUK8ZqIvrFFevghl6Sz+uayBgm9 ZgyDBtJmbhFgTzjFjIje7+BJz0hxeUofQ7DG2yAN8EBIHuhvojz8GsIoa84BUbI3BTNlb7l6h2qp hX5OelNUk+0oQBai9hED5t6ztTAP2OwdXIwFowjiOdgjZLKGwZAx4IkGT2AE9HdYalurQQnDq13G 3ajrtNDspnG940A3oVrOmgJGP6u48TR6OlKPrqgQW09qPgQkgzqKesYQhSe1aWBBBTPPLk5HlMhj QifqTnqEURYNku9o+S10q58nw2BkWKiTYborwgcPUdD5SoKYw5NefviNE14g5x2fTnkl1OKqUJFp X2oDpzF46JtJfMkDY2fYBCBdz36YE1o8FA7v+F1HZD0t9AJ5pIfH1AtXc2kfH18RoUMRXHKaGqtC koCIMLRTu1uSViM2vK9107F60fMiFDsCWkdJaAlNaBM5k7pVOpEczXQERb4bEOUmyQP1g+Bq8kI9 a6zPSO2typ/2zqMBOunxVN+zbijnrcn/s9/B+9HNu4wukw7T83vgY4leR216hq8gJ/4atfvDR50V oYfjJs+Hns6gJBFVgRtDcz/dVzWKJjMN0kSp7aOZVJUoOTQ0olmVZ+oPqI2Ppspub13/bJ1Bn6Sn Q9mwh4Cwj5FLSU92osHUX9bp5oJsxmtzvNEANSo8RezQ/Yb8kzKlrN4jbgSraGQePxDmuSVojOLE CrbqcqyF+kQ33M8MjnyrBnSfziftK/be9tLAINTDRmkGi9gWSoF0uJRmQPdpDk/PLV0ADluCZoSP uh4XUjcHhaZDfZ1WaVz1Z+fmn1Pn7yNQsd+BySZ2NSZN8qCr9RwkpDaohYCPnntNzZW8J3LxyQgZ nCh4HlBw2PIuiFGXfgIiOJORn1oF4aYzyduqVr6OVC5bY2oDZj2VxtvnL2gYGjW195CwDuajxa1D VjXHMf5Re3SKznG9lGR9z38APZJtRfP3iQpCoySVLIgVSfqkwRrWpPUkEUM4OpB0U8/7SI0B+Tr8 4EavngjWk2WX6vTvXXwFaTs0RxKfHpr6BBhp4kZ234M/dqaDPfYew6mMbmW9BlLBpxw525OazIUK mUAMwGXcSr1kdNMkijrywPdTIurPhzdiWvRchqwxmoq1AL3kzqc1R8mLvNOA5VbS4814OjiU6g2I K5LTsbJeyUc9uAQdWCJ3tek64JOE16HKgdflpIHzg5Ib0WNY3yOKVNc7uV9HTx5wIx0Q1bM9Wc/H k5Gkh+6Dv/RIlGev0aZcB1UZBYKjUrHheXBqslFzbqVeNKSnZ7bRAEfPRMfyebhR7HGEcn0GMepr DOwD53T1RJYH/zQE08pG1gRqGjGlaRkh4ClH46Vk2ztGPG/qFYgU6CiEpoPoEZrO+WY39T0k7TJQ 2+9eSP6qZ0HgKBkWPfFC4QAz8GbCwqetVtnUDH3S3lpDTLaugXwg62AuXfIkGZRwdQKIFNPTtX6p DCEaOXboGQH2UsdrSmUHjRNBsFEzEtKIE8lwBCN+Kcbz6FgMX1LzdxCFxNQzQzrl0nSIpog0GaPx ZsLDW6mVYeqGkIWI0bcXFDKyeL/HBgBAzXNnjLaUERiNBY3tDY2N5ypAxL5WUJ9OD4xoUMoVZIPX oauMWEuaurohv1lNxEjT+SI5taY/kFbMGvzCzC/SCIaeI7//9SH3oedFEE0JA6MHcqm1jI9fOB+r OPG4EYhqIuyiJxFgSz1UEwkBQG16cqXBj3oYwelOUFpZruw9qvhhh0qNjyKhG350qFeu/7G5mN8D 3n99Iet/Q2gvHBSbZE6WTCokE6BG2mO0IGvizW6RVOx505PNcDxRa3r6CQEXvB6m7UJPQttSCipM gYKGmqiLADCR8abHWNWt8hnZoqN6PVzg9B8fhKa+Yqd6UBEgiOYDNGJPhlQVp5ralw3FZr80Bx7f I0ki+aP0R5kja6ZXS0r9dY3fLD0VyIrwj5qD1YNRUf85gE2JYB81YkcilKonJjUtU7UIrCiwOXfS WXLVVKkGZ5se2YcxUZRbUK4HTXW8KV7X1O3GcRfelUxImtaMmlVzSllji4KmdTp3MtS01DPRGmwU MEUJJZXYAdbxXWp7ck8oGw0WayrjprXca1/gSlFQjcgPNfXe0A/vULFvvq0IFAhh35OkS892Q6A6 Dz49awoBbmnBAVO6p4LZ0DCvBpkqea4BMMqcOtGRVVZ7RA+qDT0llvFLbKz+xwmKHGJPXNcl6RxI nIxPwEltOneJ1aLO+kMEVr8tCP8f07Hdu4f7X5ELM70dRvn/AAABhGlDQ1BJQ0MgcHJvZmlsZQAA eJx9kT1Iw0AcxV9TtUUqDnYQcchQRcGCqIijVqEIFUqt0KqDyaVf0KQhSXFxFFwLDn4sVh1cnHV1 cBUEwQ8QRycnRRcp8X9JoUWMB8f9eHfvcfcOEOplppod44CqWUYqHhMz2VUx8IogugCMYkRipj6X TCbgOb7u4ePrXZRneZ/7c/QoOZMBPpF4lumGRbxBPL1p6Zz3icOsKCnE58RjBl2Q+JHrsstvnAsO CzwzbKRT88RhYrHQxnIbs6KhEk8RRxRVo3wh47LCeYuzWq6y5j35C0M5bWWZ6zQHEccilpCECBlV lFCGhSitGikmUrQf8/APOP4kuWRylcDIsYAKVEiOH/wPfndr5icn3KRQDOh8se2PISCwCzRqtv19 bNuNE8D/DFxpLX+lDsx8kl5raZEjoHcbuLhuafIecLkD9D/pkiE5kp+mkM8D72f0TVmg7xboXnN7 a+7j9AFIU1eJG+DgEBguUPa6x7uD7b39e6bZ3w9cRnKeodbOXAAADRppVFh0WE1MOmNvbS5hZG9i ZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3pr YzlkIj8+Cjx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IlhNUCBD b3JlIDQuNC4wLUV4aXYyIj4KIDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcv MTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+CiAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9 IiIKICAgIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIgogICAg eG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2 ZW50IyIKICAgIHhtbG5zOkdJTVA9Imh0dHA6Ly93d3cuZ2ltcC5vcmcveG1wLyIKICAgIHhtbG5z OmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIKICAgIHhtbG5zOnRpZmY9Imh0 dHA6Ly9ucy5hZG9iZS5jb20vdGlmZi8xLjAvIgogICAgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRv YmUuY29tL3hhcC8xLjAvIgogICB4bXBNTTpEb2N1bWVudElEPSJnaW1wOmRvY2lkOmdpbXA6OWE2 NmQ3M2QtZjRhMi00ODM2LTg1ODAtZDg5MTlkZjJiNTVmIgogICB4bXBNTTpJbnN0YW5jZUlEPSJ4 bXAuaWlkOmQ1Mzg3MTc0LWM0YzctNDM4Yi1hYjkwLTgxYTZiY2Y2OTUwNSIKICAgeG1wTU06T3Jp Z2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOmVmNmNiNjdkLTZkOTktNGE0Ni1iOTUxLTU0MjkwMzNj ZmYxZSIKICAgR0lNUDpBUEk9IjIuMCIKICAgR0lNUDpQbGF0Zm9ybT0iTGludXgiCiAgIEdJTVA6 VGltZVN0YW1wPSIxNjU5NzQ2NTI0ODU3NTIzIgogICBHSU1QOlZlcnNpb249IjIuMTAuMzAiCiAg IGRjOkZvcm1hdD0iaW1hZ2UvcG5nIgogICB0aWZmOk9yaWVudGF0aW9uPSIxIgogICB4bXA6Q3Jl YXRvclRvb2w9IkdJTVAgMi4xMCI+CiAgIDx4bXBNTTpIaXN0b3J5PgogICAgPHJkZjpTZXE+CiAg ICAgPHJkZjpsaQogICAgICBzdEV2dDphY3Rpb249InNhdmVkIgogICAgICBzdEV2dDpjaGFuZ2Vk PSIvIgogICAgICBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjE2NmRhYWY4LTEzMDAtNDgyOC1i MjYyLTAwN2FjZTg5YTMyZCIKICAgICAgc3RFdnQ6c29mdHdhcmVBZ2VudD0iR2ltcCAyLjEwIChM aW51eCkiCiAgICAgIHN0RXZ0OndoZW49IjIwMjItMDgtMDVUMTc6NDI6MDQtMDc6MDAiLz4KICAg IDwvcmRmOlNlcT4KICAgPC94bXBNTTpIaXN0b3J5PgogIDwvcmRmOkRlc2NyaXB0aW9uPgogPC9y ZGY6UkRGPgo8L3g6eG1wbWV0YT4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAK ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg IAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAg ICAgICAgICAgICAgICAgIAo8P3hwYWNrZXQgZW5kPSJ3Ij8+vQWh3wAAAAZiS0dEAAAAAAAA+UO7 fwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAAd0SU1FB+YIBgAqBHHUThMAACAASURBVHja7V13eFTF Fz3zdtN7J430QkJJ6B2kVxGk2RACIqIoKtgL9oIKiIpSEhAR6UV6k44ESOghnYT0tpu6u9ndN78/ KD8h5b3dbM+e7+ND2VfmzZx33507954hMEMjiA6c3qYeJIAhTFuWYb3BEneGUE8K4gXAA4A7KCwA OIGAAWAJwO7e6bUA6gHIQVEDBjJQlIKgGKDFoCgFJYWEoZkMy2SmZPvfARax5l5vGYi5C1QgePQk y/pauygwgigCtj0lJJpQRAJoC8Bah02RgSALlKZQwiQTqkyyYJF04/baIvMomcmvEQQHz24rJMre lLI9CUEPUNL5nsU2VOSDktOU0ONQMifSc1anmEfRTH6+ZHcSEsUgSslQAjoUQKiRP1IRKA5Rgt2W NtYHb9z4pcY8ymbyP0B4+Gx3qlBMJMBkAP0ACE30UaUA/iEU2+REuDUra2WlmfytEIGB050tGDIe hEwhFINNmPBNvwgEfxMWf9i7CvdfurRSbia/iT9rZOCMYUoBmUsohgOwMn/4AQCFlGI1FTArMzJW 55nJb0KIjp5rL6+TPg+CeQAizFxvEgqA7mYZ8l1GRvw5M/mN2pePC4YCswG8AMDVzG2VcIal5JuM 7DV7AFAz+Y0EwcGz2wqp4n0QzAQg0NV9GYbA3cMJvj5uaOPjCjc3Bzg52cPZxQ4uzvZwcraDs/Pd /yek8W6XyeQQi2ogEtWgoqIaFeXVEItrUV5eheysItzOLkZNjUSX3ZnIUvJ5Rvaav83kN2BE+Mf5 sBbkAwI6C4CFNkkeEuqDiAg/hIX7IjTMB2HhvvDxcYWFhfbnzcXFImRnFSE7qwipqflITspAelo+ lEqtLvgeI8DbqVnxF83kNySf3m+mq9ySvkWAeRSw1fT1bW2t0L1HBGJiQxATG4IOHQNhb29jUH1Q WyvF5eQsJCdlIOlSOi5eSIdMpvEADgXBHyyhb2ZkJJSaya9XLGLCg+/MAegXAJw11iGEICq6Lfr2 i0bffu0R2zlEJxZdk6irk+H0qRv45+hlHP/nKioqqjV5+TJQ+m5adkC8secXGSX5I4Ond6CU+Y0S 9NLUNdt3CMTI0d0wcmRX+Pq5m8ynXalkcTk5E7t3/Yt9exJRVVWnqUtfoix9Of12wnkz+XUAP79J NraWDm8DeBcayLEJCPDExMn9MGp0d/j5mw7hm4JMJseRw8nYvu0Mzp25qYl5ghLAdxY21R/duLGl 3kx+LSE8OG4IKH4DQXBLJ6w9e7XDlKkDMHR4ZwgEDFoj8vPKsPHP49i08YQmvgbnlYQ8m5m5JsNM fg1i4MBFwoLc3A8AfAhAbaY6ONhg6tMD8dTTA03KrWkpqqrqsPmvE1i/7iiKikQtuZSEgLybmrVm mZn8mvDtA6cHsgKyEZT0VPca9vY2mDZ9CGbEDYOjk62Z7U1ALldg6+ZTWPHzXhQXi1rCqPUW1tWz jMENMljyh4fEjQfFGgAuZtLrdl6wYf0xrPh5T0vcoWMsIxufkbGhykx+FRAaOs+KsLVLCTBHnfMF AgZPP/MY5r02Dk7OdmY2q4mKimos/X4Htm45pdbEmABJ9ZQZlZ29uthMfh6I9pvpWm/BbiOEDFTn /C5dw/DRomcQ2c5f4227lXIHXm1c4OJi36pegpSbufjy87+QeD5VndOzCcXI1Oz4VDP5m3Nz7iai 7QUQqeq57h5OWPj2RIx7oleTeTPqoKCgAtu2nEJpSSVemDMS/v4erfZLsG9vIj7/ZCPKy1X2ZCoY Qsfeykw4ayZ/Y8QPjusLYAcAlcMwT07si3c/mAoHB82kHFBKcfZMCv784xj+PXcLbyyYgKeffUyj L5WxQiSqwacfb8C+vYmqnloDgslpmfH7zeT/DyKCZk6ihK4DoBJ7nZ3t8ekX0zB8RBeNtEOpZLFn 93ms/HUfMjIK0K17OL76Nq5VW/umcOhgEj75aD3KylT6CshByOS0zDU7zeQHEBYy41VCyVJV29Gn bxS++jYOXl4uLW6DQqHEls2nsOq3/cjPK4NQKMAbC57EjJnDwDBma98UxOIavPt2Ao4duazKafWE oZNTMxJ2tWryhwfPeB0g36vSBktLIRa8NRHTpg9psRtCKcWBfRex5PvtyMkpAQD4+bvjh6UvolNM sJndPPtwXcJhfPftNsjlCqP7AhD9ED/uDQDfq3KOm5sjflrxMjp3abmayJXLWfjskz9x7Wr2g38b NCQG3yyeCUdH85qAqrh6JRvzX/0V+XllRvUC6Jz8YSFxCwnFt6qcE9nOHyt+mwcfX7cWT9i+X7wN 27acAsverc4jhGDuK2PxyquPm92cFqCqsg6vzVuBs2du8neBKCalZsfv1lebBTolftDMBQRYrMo5 Q4bG4rfVr8HVzaFF997z93nMjluGpEvpoPeqUu3srLHkxzl46pmB5mhOC2FlbYGxj/dEZWUtrl7J 5sc9giddnbucrxAnZZm05Q8PinsBBCtVOefFl0Zh/hsTWmSRy8ursOjD9Th0MOmhf/fycsHKNa9p ZUGsteOvjSfw6cd/8F0ZrmIZ+lhGRkKSSZI/PHj6KIDZBZ7iUIQQvPfBVEybPqRF9z3/7y28+fpK lJY8LE4WFuaDlfGvw8fHLOqgLRw7egXz563gW05ZKgDbJyVrbbpJkT8yZFYXlrLHAfDKC2AYgk8+ m4bJU/urfU+lksXyZbvw24q9D3z7++jVux2W//KyxhbFzGgaZ07fxMsv/QRJnYwPFTPllPTRZS6Q VskfETAriArYswDa8HICBQw+/2o6JjzZR+171tRIsOD1Vfjn2JUGvw0Y2BHLf5kLKysLMzN1hGtX szFrxlKIxTw0cgn9lyX2AzMylst00TatTXiDg2c7EUZ5mIBf5ZVAwGDxDy9g3BPql+VmZhRi2jOL cTk5s8Fvw4Z3xo8/vwRLSzPxdQkvLxf07ReNQweTIJFwpfgTPwJ5QLkoeYfRkn/gwEXCGnHFPkJI d77EX/bTSxgxsqva97ycnIXp075DcSPVSKNGd8cPy16EUCgws1EP8PBwQq/e7bB3TyLq6zkXwzq5 OneurRAnnzVK8lsJgr4ByNN8j//si2kYO07tYi2cOX0Ts2ctRU11QzWz4SO74oels1ttra6hwNPT GdHRbbFv74UG87CGAQ8McXWJSaoQXU4zKp8/IijucUqwk++1574yFq+9/oTa9zu4/yLefH1Vo8vr vXq3w8o182FpKTSz7x5kMjmKikQoKqxAYWEFCvLLUVJSieqqOtTUSFBdfffPfyepEokM9ff6187W Gja2VrCxsYSjoy1sbCxha2sNN3dHeHo5w8Pd6e7fnk7w9HRusGK+d08iFr6xik8YtJplaM+MjISb RkH+0NDnQxhWcAmAE5/jJzzZB19+M0PtBabNf53Eoo/WN9qR7TsE4vcNC2FnZ90qSc6yFBnp+Ui9 lYe0tHxkZBQgPTUf+fllnJZXk3B2tkdQcBsEB7dBYJAXAoPa4EJiKn5fe4TP6akKCHtoayMNjZF/ 4MBFwoI7Oaf4Fpv37d8ev616VW0/fNvW03j/nbWgtOFABgW3wZ+b3oGrq0OrIbtMJseli+lIupSB 5ORMXEnORHW1xPgfjNB/CUtWg2WOpeaszjZI8ocHx30MYBGfY6Oi2uKPv95W2yofO3IZr8z9uVGL 7+hoi607PkBAoJfJEz7ndjFOnLiGUyeu40JiKo9oitEjG4QcJcA2b3//I8ePL1LonfxhgTN6EIac Bo8VXCdnO+zY9ZHa2jn3ozqNLZwwDMGKla9i4GMdTXb0C/LLceRIMg7su4hLF9PRilECYAtl6Xp1 JRNbTP6OXs/ZSewskgkQxnUswxCsTngDffpGqXWvtNQ8PDP1myYlNV6dPw4vz3vc5Ea5pkaCPX8n Ysumk7h+7bZ51t4Qlwgl36Rmr9kKFTbTaHEYRGpr8QMf4gPAvNfGqU38stJKzJqxtEniDxoSg5de HmtSI3or5Q7W/34U+/Ykoq5OZqZ40+hCCd0cHhyXSFn6Kt8vQYss/72wJq+StJ692iHh9zfVytBU Klk8/+xiXEhsPOzr4emEvfs/MxmdnnNnU7B65X6cPnXDTGvVoSSEfO3t77+Ia06gNvnvbQqRAsCT 61hHR1vs3vcJvL3Vy6L8+stNSFhzqMnfV6x8FYMGdzL6UTt98jqWLd3JNx/ejOZx1EphNela7gqR xt0euSU+4EN8APj402fVJv6BfRexNv5wk7+Pn9Db6Il/6WI6vl+8zWAnsJaWQtjYWsHRwRYWlkLY 2FjCxsYKlpZCODjYgDAEAoZ5sGONlZUF5AolWCULhVKJ2lopAEAsqoVYXAOxqAZicS2kUq1GpwZL hbKTISHPDc7MXF+iMct/L1szBTz2sh07rie+++EFtVp/O7sY4x//pEl/18vLBXsOfGq0dbeFhRX4 9qst2L/vQqPrFdqGlZUFfP3c4ePr9mAjPS9PZ7i4OsDF1R6urg5wd3fU2kJhTY0Ed3JLcedOGfLu lCI7uwg3b+QiI71Aky/GNQWE/RpbKFPL8lMB+wUf4vv4uOKjRc+o57gpWbyzcE2zE72PPnnGKImv VLJIiD+En5bt0npsXiBg4OfnjpBQbwSH+CA4pA1CQnzg5+8Od3dHvfaDvb0N2kW1Rbuotg36JzOj AInn03DhQirOnrmJqkq1RXM7WECxGZg0CtiibJHlDw+e3g1gznOdyzAE6/5YiO491Nv3efWqA1j8 9ZYmf+/aLRwb/nrb6IifnVWEd9+KR3IjadcthZ2dNSLb+SMqqi3aRd8lVWioj9HnNsnlCpw9cxMH 9l3EkcPJ6qlHUyxIy47/voXkjzsKYBDXcc9OG4QPP1bP6mdkFGDC4582WQJHCMHGLe8iNjbEaAaQ Ze9q3Cz9YYdGPukCAYPQMB/Edg5FbOcQxMSEoG2Ap8krUMjlCuzfewErf92H9PQCVU6VUSUTm56z OkUt8ocFzxxNQPdwHefgYIPD/3ytlqKxUsli6qQvm414DB3WGT+teNloBqygoAJvzv8NSZcyWkT2 Dh0D0bt3FLr1iECnmOBWm7QH3BXM2rP7PBZ99IcqG3OfTsuK7497C2EqfQ8J6Ht8jps9Z5TaUt6b /zrRLPEFAgZvLJhgNIN05vRNvDn/N4hENSqfGxLqjd69o9CrTzt07xFprjt+5Os/dlxPdOgYhLlz liMzo5DPaX0jQmYOT81cc0Al8keGzhrIsmxvruO8vV3VVl0Qi2uw9IfmRbxGj+2B4BBvo7BMK3/d h2VLdvLe3MHD0wm9+0Td/dM7Cp5ezmaWcyAwyAvr1i/EM1O/fiA7yTEuCwCoRn5Wyb7Nx0l67Y3x sLZWb5fQZUt2chY6z5g5zOAHRKFQ4oN312LHdu5KvOAQbwwZGoshQ2PRsVOQWTxLDXh4OmH12tfx xJhPHqwpNIPBoaEzOmdkJCTxIn94UFwnEAzn/Dq081e7AP1Wyh1s2nii2WO694hA1CNhMUNDba0U 8+b+jDOnGy9AYhiCDh2DMHRYZwwZGoug4DZm9moAbdt64p33puDD99dxHssoyXQA/MgPgrf5TI7n vDRa7WjDku+3c7oHT07qa9ADIKmT4YW4pQ1WagkhiIkNwePjemLosM7w8HQys1ULmDSlHzZu+Ac3 b+Zy8XkYr2hPu6CZAUpCM7hcpLZtPXHgyBdqFYpfv3YbE8d/3uwqp42tFc6eXwJbWyuD7Pj6egVe nvMTTp649pBLM3pMd4x9vEerKK4xBOzfewHzX/2V2/qzbBCn5VcS9kWAcB4368URaiskLPlhB+fy /uDBMUZBfEcnWzzxRG+MG98L7TsEmtmoYwwb0QWOTracK8KUMIObJXV09CRLuYTM5DPhGD9BPZW1 5KQMnD55nfO4xww0eY1lKRa+uQrV1RJ8891MjBjZVe0Jvxkth0DAoF+/9ti7J5GD/DSkWfIr6uwn g3Bnbk6fMVTtJfQ1qw7yeqD+/TsYZGffzi7Cy6+MRXiEn5l5BoKY2BBO8gOMPwdjyVyuG9nYWGLy lAFqNTLvThmOHeXe0ykqOsBgd1E3hjWH1gY/HvXhhNCmyR8ZPL0DC3DGLceO66k2MRPiD/JaADKm HB4zgOpqCaSSekhl9VDIlairk4FS+iAhraZGClbJok4ig1yuQH29AlJJPZRK9kGqQnVVHSi9GzpW KJWQSeVQKJSws7+b0uHoaIvQUB/06ReN0FCfh+7v4sqdXUApXJskPwtmOp8HffqZx9TqoLo6GXZs 4yfH2CnWvEGcNiGpk6G6WoK6Ohlqa6Worq5DXV096uqk9/7/roJbXZ0MtTXSe8dKHxxfUy1BbZ0M knv/r2v07d8en33x/IP9FqRSPnsCEGWj5B84cJGwIDeXU2sztnNog1xsvti/9wLvjnr0zTajcSiV LEQV1ai496e0tAqV4lpUVdVCLK596L+rKusgrqxFVWUtH/FYg8bpk9fx5LhP8dfW9xAQ4Imqyloe Z9GqRslfcCd3KHho6j/19EC1G7xl8ylexzEMQWBQ646Ri0Q1KC0Ro6hIhNLSShQVVkBUUYPy8mqU lVWiorwaIlENKiqq9VIRZgioqKjGqy//gu27PsL16znc1AdKmnJ7xnOdbG9vg2Fq7n6em1uC5CR+ 6b0eHs4mHTosK6tCQX753T8F5SgsrEBpaSWKi0QoKRGjpFhs9JZZV7iVcgfLluzgFTonBGmNkH8R A5o7huvkkaO7wcZGPVIeOnCJ97Et3YXRECZ/WZmFyMsrQ0F++YO/8/PLkJ9Xru0i7laH31bs43kk vdqA/KGBed0BcMbvxoztrnYDH90ZsTmoWxegL+TmluDMqRs4dzYF167dRkF+uZmRKsLWkoVQeNd9 s7dmcT9vQKYgKK3SSEkmFSgsTja4kkBAx3C5jY5OtujaLVytuxYViVTSpTGGaqWaGgl2bD+L3TvP tWrNHQFDseftbDAqCG/bWrEQEkAgoLCz4g57l9cIsGSPB3Yktig58FpKzsrCBuSnlHImzA8Y2FFt afEjh5JVmpQZcnp7pbgWq1cdwMYN/5iGHHgLoWQJBAzg6yrX2j3c7JX4dEoxbuVbISVfXcNIdgKP ZGoGBk53BtCZ69QhQ2LVbvy5szfvkZrAwfFuWZ5SweolPqz2ICtZbFh/DD8t341KcS3M+D9yyizh 6ybX6j0YQjGpVyU+3aoe+VmG3dWA/BYMMxgc+3QJBAz69ItWu+Fz543Fu+9PhVcbZ1hY/P/2Mpkc GekFuHbtNg7su4Dz/94Cy1KeCxa69enfemO1VqRHTAF3ynWz22WvMLWNztWMjITkBuQHyGAuhecO HQNbVEgdHR3Q6L9bWVkgun0AotsHYOpTA5CZUYgvP9+oVuG3tvDPsSt4c/5Ko/pKmSr523rI4eMi R4FIpfspKGUW4B7JH0nAp/24zu7dO0onDxcS6o3VCa/jifG9DWJQf193BC/P+clMfC63p1R3azJd Q1SaZ5VQYGp69uoHwq/Mf/19AnAyu1efKJ09HCEEz04bpPcBXRt/CF98upG3CkNrRm6Z7jb57tiW F/krCcU4YoHQ9Kz4bf/94YHbYylgeoGi2VIsoVCATjGtK8lsx/az+OqLTWZW84SG4vC8EOnLa8MO +2qZ8EhB9soGpV0PyE5YyulfRET4wcrKotUMZNKlDHzEQw3AjP+jSiJAvUI38ekIXxkYwhk2Fzja sO0a++EB+VmGdOO6SodOQa1mEKurJXhz/m/mvBoVQSlQVq0b629rycLPnXt8WND2jXoyDyw/RQwn +dUsyC4rq8K/51JwJTkLOTklEItr4OrqAG8fV/TqHYU+faMMbiX3q8//QkFBhZnNaqC8WggfF92E qNv5SpFb2rw3QsA2Tf52AbO9lVBw5g136Ki65S8uFmHr5lNIPJ+Gy8mZDRK5/vzjH9jYWmHqUwMw 64URcPfQv6bN1SvZ2L7tjJnFaqKsSqCze0V4y3DwsgOH5ScdmnR7WKGSUxrBxtYKoWGqF5V4ebng 5XmPY90fC/DvhaVY8uOL6PiI+ySpkyFhzSEMH/I+tvLM89cmvvlqs87y4jsHmV5aRHm1YU16CUVk k+SnLOVcso2ODlBbl+e/L9Co0d2xZfsH+H7p7Aa1vzU1Erz/7losfHM15HL9+NrJyZm4eCFNZ/eL G1SBEC/TSmuukjI6u1egB6++8x04cJGw8QkvAWf8Mrp9gEYbPWZsD+zY9THatm2ojLJ75znMnrms yc0ptInfE47o7F4udkr0iajFmC5VJkX+OpnuyN/GWc4n+VFYnHPHt3HLT8ApjxCkhVJCP393/Lnp HXh5uTT47eyZm3hz/kqdLizV1Ehw9Eiyzu43vnslLIUUE7qLIWRMp/xQUq878ltZULjZc3sJcsoG Nkp+QrnJ7+/voZXGe3g6YenyOY2mSB8+lITly3bprCOPHbmss6+NkKGY2kcMAHB3VGJU52rTsfz1 us1D9+YRWWIETGPknyQAwCnB4B/gqbXGd+4SijcWPNnob7+t2IvE86k66cSTPGo/NWf1qx7Ke39p WDkEJmL9pTq0/ADg48pjfkjZBn47Exlo5w+g2WwkgYB5oImiLUyPG9qo+hnLUrz/zlqdWORLOpro 2liymD304fLGtu71eLybafj+Eh1bfj5rChRMAzUShmUEnC6Pt4/rQ7n32oBAwOCVVx9v9Lfc3BL8 vla7E9GysiqdLWrNHV7e6IAtGFsKN3ul0ZNfJtex5XfmtvyEULeGbg+lnJEebfn7j2LkqG5NCr6u Xnmg2Q2pW4qc28U6ecZIXxmeHyBq9DdnWyUWPl5i9OTX9W6oznwMBkUj5Cfw5SR/W92Qn2EIJk/t 3+hvYnENtmw6qbV75+aWav35nGyV+GFaQbO+/diuVZjcW2zU5Cc6nrs42PAifwO/naEAZz6Bp6fu dgUcPaZ7k8XxO7ef1dp9K8XarRgTMhRLni9AAI9FmfcnlKBnmPHWBjM6vp+DNXc4vFG3hwCczNbl /q+urg7o1bvRDFTcvJmLjIwCrdxXmy6VkKH46pki9AirU+FFKUT7tsZZNcYYoOWnIK6N+PzEhfPi jrrVxu/Tt+lsi907z2nlntpKXXaxU2LFC/kYFataJMfRVomEuXcwuH2N0ZFf12oz9ta8FkLtGlp+ hnJafkcdk79rt7Amfzt8KNloSDC4fQ22LbiN3hHquTC2liyWzcjHx5OK4WxrPFEgRsd+j4MNL/KT Ll1mP5T7LKQULoZG/qjoANjaWjXqimRnFaG6WqJTV0wlq0eAoR2r8Vx/kUYyNgkBJvcSY0Snamw6 64SNZ1xQXCk0aPLbWeu21tnGgoWAAbgyYaRSoRUA+X98fm7L7+Rkp9OHEQgYhDShyU8pxY3rtw12 4Cm9W8dqJdSs32shpKhXElRJGBg6nGyUOjc4PMoZoVTWPLSdJ0NBHA1pwnsfgYFNp1Ncu3rboAc/ OdsGU5YGYNHmNpDIiEauN+H7QPxy0F2nSWNa9sF17mqxLB4qFxSCY3NpALC01H3RetuAprNIb6Xc MXgCUAps+dcJl7Kt8eP0AgSpkbOvZIHlB9yx5qgrWEpgLHC00f38hE/vEJnlQ2k8DL+B1H3Clbd3 01ORigrjyYDMKrbC1GUBOHpdNan1yjoBXlrth1VH3IyK+Pqy/IQHk6kVS1QmP9GDVLK9fdOulrGJ w9ZIGbyW4Iu/zvBbLCyvEeD5n/1x5pYdjBEOeohMMeDj81P5o+TnPEsflt++mXmGSGx8sW9Kgc+3 e+HPU80H18qqhYj7xR/phVYwVrRx0n0JKh/7bN0I+bkHDnogv33TUiZVlXVGSQpKgS93euJYEy6Q TE7w4ko/ZBQZL/EBwMtZD+TnY/mtWdUtvz4glzf96TRm1ThKgfc3eiO/ETXjr3d54la+cRPfxpKF i53u3R4+UbD6emuFyuRnWd2/H83l2qi747uhoErCYOEG74cmsoevOmDzWWcYO/Rh9eVKAgXL7ffY 10oakJ9zGVIfstx1dU3f08nIyQ8AV27bYPdFxweD991uD5gCdKXU9pDV56kW4douRPYw+QlEnJZK Dz52VZWkGfJrfodGfUS0Vh11BaXA3iRH5FWYhgCwPix/Hb/Ksdrjxxc9bPkJCzE3EXVP/rw7TReX eGuhnrilglzq4HaJJa7lWmPXBUetXF8fBfEhnroX4JJIeRgugspH/4mhhPCw/LqPq99phvztNSyg BQCMQD9pAydT7HH5tubTR5xtlZjUq1LnzxPqLdM9+flZ/kbID8pN/ird60neyWma/FFaIL+FUKAX 8h+7bq8VPfupfcSIG1gBXb/T+pBerOWRP9WYh8MQwof8urX8Umk90tLyGyephRDh4b4avydh9JNC oI09rFzslHi2vwi+bnKM66Y7629vzcLbWfcT3goewri0UcvPcrs9paW6/Xxeu3q7SaHamNhgrcio 6MvyS+WafekYQvHZ1KIHsfaFj5eolVSnlsvTRqaXTcN5qUIzaFBKxzCEFnKdl59XrtOHuXQxvcnf Ro/prpV76svn1yRsLVksfq4Qj0X/P/3D0YbF6jl5OqkHDmujH7VpUR2fsaMljU14b3OdVpBfpls/ +OjlxqMXAgbDRnTRUmTEuMnvaMPij1dzMSKmYcZrGyc5Nryai36R2nVfY/S01wCfbZAoi6KG5Fcw nOTPy9ed5b9zpxRXr2Q3+lvffu3h5qalsKDQuMlfJWHw/E/+je5SUl4jwNzVvjil5SzR2ED9kJ+P z8+ANCS/RFl5GxwpDpI6mc5y6Pf+ndhkFmlTglaagFAggLGjWirAm+t9Hkqdzi2zxJQfArWeHu1i p0Rbd/24PeU13GNHCdvAvWfy8rZIAHBq5OXnad/1kUrrseGPY437k+G+GDwkRmv3NnbL/2CQKfDF dk+cT7eFTE4wL94HhWLtF7zHBEr0Mtm9O+HlJj/LCBqS7FtWbAAAGYJJREFU/97fnK5PVmaR1h9i 08YTKClufMF5wVsTtZqCYOw+/0MDTQkW7/bAjkQnnaVHx+rJ36cUKK7kTg2xkAuKGiU/Ac3kOjk1 Vbt1sxJJPVb+tr/R3/r1b4+Bj3XU6v2FQgFMCSn51tieqLudLfW1sV5plRAy7nCx0ivIp7QJy89c 5Tpb20Xjy5bsQFkj6wmOTrb47Mvntd6Julal0wVu5ulmb2NHWyU6BuhHWrFAxCshsODRpLb/k5/g Cqflv5WntQc4dfI61iUcbjhDZwi+WTwL3t6uWu9EFxd7kyO/rqpP+0bU6m1XGZ7ZsI2GDxkAYBQC TvKXlVU1aplbihvXc/DGa781WjDz7vtTMWhwJ510orOzHcxQk/zt9CcoUMCH/JQ2Tf6UnJWF4BHx SdGw6/PvuRRMf+67BinThBC8ufBJTJs+RGed6GyCll8XYAhFn4g6wyY/IU2T/x44/f7kpEyNNLi2 Vorvvt2KuOd/aEB8GxtLfLN4JmbPGaXTTrSzs4atrZWZzSoiyl8GdweF3u6fL+IRxiWNuz0PziQU yZSgWVPb0p3J01Lz8Pfu89iy6SREoobyI527hOLLr2cgKLiNXjoyItIfyUkZZkargEHR+pWRuV3C IyuWZZsnPwT0DFiysLlrXL2SBYVCqVJYsLZWig3rj+HPP/5BYWFFo5Pa3n2i8Mxzg3Xm3zeFdlFm 8quKkbH6U8+T1DMo4rGAxwqEzZOfMhZnwCoompE9lEjqceN6DjrFBPNuoJ2dNWbPGYUJE/vi6pVs 5OeXobysCo6OtggM9EJM5xC4ujoYxic8qq2ZzSqgU6BEbykNAJBTasFHylHu5+dXlJHRDPnT0laW hYfE3QJFu+audOliukrkvw93d0e9W3Zuy28mvyoYFatfzdSsEh5zNIKMxmL8j054ARanuK515vQN kx3MdlFtTTLerw0whGJYR/2Sn4+/TyhuNvkMjxx6mutiiedT9aLjowsIBAwGDOxoZjYP9AiTwNNJ YfDkB6Up/MhvIT8GjvTm+noF/j2XYrKDOnhorJnZPDChR6Xe25BezE1+ljD8yJ+W9ns+eMT7Tx6/ ZrKD2q9/e9jYWJrZ3dz8zVGJoXp2eSQygswi7nGiDHha/ruH7+O64D/HrupFv1MXsLGxxMBBncwM bwYTe4hhIdDv+F/Ps4aSW5+TrasTpKpAfsJJ/uJiES4nZ5rs4D4/Y6iZ4U3NixjgyZ76d3lu5fPI WKW4XVCwso43+dOyqs8BqOC67r49iSY7wLGxIYiJDTYzvREMiKrRixjto0gt4BXmbNaFb8Tyb1Hy sf7791+EUsma7CBPnzHMzPRGMLWP2CDakcZr5xqiKvkBlmIz12XLSiub1dcxdgwb0QWBQV5mtv8H UX4y9InQ/35olPJb4KIgV1Qmv5Vt1UE+rs/2radNdqAFAgbvvj/VzPj/4IXB5QbRjqJKIa/9jSkj V538N25sqQewm9P12XdRL/LlusLAxzqaF73uIcirHkM6GsZGgLwWt4CajIygbJXJDwCEkE1cV5dK 67F/3wWTHvT3P3oKlpbCVk/+WYMqwBDDCG9fy+WWdCcU14BFrFrkT82sOgwgn+smm/48YdKDHhDg qfPCGkODt7MCoztXGUx7rvDYz4Al5DLXMc2I1WxRgmAd1wVu3MjB+X9vmfTgvzzvcXTvEdFqyf/S sDK9L2rdh4IlSMrmJj9D2bMtID9AFMxq8NitMX71QZMefIYh+Pb7WXBqhUXuQV71eKK74Vj98+m2 qJJwC4xRC+U/LSJ/as7qbADHuS5y4vg1ZGYUmjQJvL1d8dnn01od+d8cU6o3WZLGcOgqr5Tz1Ht5 auqT/675x3LOt4xSJMQfMnkiDB/ZFfNeG9dqiN8lWPKQ1r++UStjsD+JW6Wbghzj9UXnOiAts+0u AJxO/a4dZ7Wi62NoeOXVx/HUM4+Z/HMSAiwYW2JQbdqR6IRaHnvuMoRu4XM9HpXox6m7SwwLkNHN HXU/1aFvv2iTJ0a//u1x9Uo2cnNKTPYZR8ZU47kBIoNpj6SewYL1PtzkJyhIy6yeD9zk9NV4SRNX Sy3WAeDUKN+w/lijCg2mBoGAwY8/z0W37uEm+Xx2ViwWjjOsF3v9SReUVPJYb2Gx7W5+mkYsP1Bd fUnu6hzrQAgGcFn/6moJBg8x/WooCwshRo7ujhvXc0zuC/DGmFK9qrA9itxSCyz8wwcKltdm01Fu LrEXykXJWRqx/ADAMvKfAHAW7+7cfhbpafloDbCxscQvv83D0GGdTeaZwn1keKaf2GDao2QJ3v/L W5VdKx0A7AkLnNFDI5YfAESiq7VurjFtANLsdoiUUqSl5WPCk320upmEIblAw0d2RWFBhca1TPUx yf1xRoFB5Ovfx7e7PHHwisq6TkJCyBAbu9CVVVU3FS0mPwA4OndNEhA6B0CzmUUFBeXw9XNvNTo4 DEMwZGgsnJztcPb0zSb3FDN0TOhRiaf7Go7V33TWGcv3u6t7urOFwKqiXJR8TiPkF4uTal2dY224 fH8AuHQhHRMn92tVxeCdYoLRsVMw/jl2BfX1CqNqu5eTAsvj8mFlYRgv7t8XHfHxZi9QtMh78C8X Jf/SYp//Pixtrb8FDzlzsbgGi7/ZgtaGfv3bY+OW9xAQ4GlU7s4nU4rgaGMYlXl/nHLBexvb8JEi 5EJUdOD0Nhqx/ABQWnqh3s01tgrAWK5jb6XcQUxMMNoaERE0ATc3B4x/sg/y88qMYvI/uZcY0wwg pi+VEyza2garjrg1a/E9vZwR2yWUV5RNCebvCnFyjkYsPwCkZcavAgintCGlFB+8tw41NRK0Ntjb 2+CHZS/i8y+fN2jXz89VjoXjSvXejpt5VpiyJAA7zje/iV73HhHYsftj/LpyHpyd+eT5ECuNuT33 ec2AmQUeoc/Cwgp889VmtFZMmtIfW3d+iMh2/oY3UScUXzxVBFtL/bk71VIBFu/2xNSlAc1umyoQ MHjxpVFYu34B3N0dsX/vBYjF3HlHAqIo0pjbcx9loqRyN5dYIYCBnG/1jVxERbXV26YT+oarqwMm T+kPGxsrXLyQZjCqFzMHi/CknmQHJTKCTWdd8PpaH/ybbgvajH/v4+OKn399BRMn9wPDEFBK8c5b 8XxyyWqUjP3bFRWJja74tmhGERo6z4qhtclcsub3CbB77yfw8HRCa0ZOTgk+eHctEs+n6rUdnQIl +P2VOxDqOF05r8ICm886Y+u/Tqis47a9I0Z1xaefTXuoluLAvot4bd4KHv4J2ZSWvaZJFYIWT6dD A2f1ZBj2DB8Xqm+/aKyKfx0MQ1r1C8CyFJs3ncSPS3aivFz3hSKudkpsWZCDNk66WcyqkTI4fcsO ey454sRNO15RHG9vV7z/0VMNVs+VShZjRn6ErEzu+hGG0D63MhPOatztuY8KcVKeu0uMD0C6ch2b m1sKOztrdO4S2qrJTwhB+w6BmPLUAFBKceN6js5cIYZQLJlegCg/7crMF4qF2HvJET/ud8dn27yw /7IjbpdacsbthUIBZr4wAsuWv4SIyIbzpC2bT2L71jN8evlUalb8p80eoYkHDQ6e7SSE4joAP65j hUIBft+wEF26hsGMu8jPK8PSJTux9+/zWn8JXhhSjvmjyjR6zbp6BukFVkjJt8Ll2zZIum2D/HIL la/To2ckPv7kWYSEejf6e6W4FsOHvNfoZoYNvq6UPJ6RveZvrZMfAMKCZvYnhB7j8zXx8HTCjl0f t3r/v7H5wMpf92HXjnOQyzW/QixgKCb2rIS7gxLOtko42SvgYsvCxV4JJ1sl7K1YSOUE9cqGtBDX CCCuE0BUI0BJlRB5FRYorLBAdokl8kUWLdrtPSLSD/NeG4chQ2ObzQf7+IPf8ddGXmohl9Ky4ruB o/5co853RFDct5RgIZ9jO3cJxfo/31JpZ8eW4sTxqwiP8IO3t6tBvwQFBRVIWH0QO7afQXW16a6R hIX54JVXx2HYiC6c88ALiWmY9sy3vKTxWYrhGdncdbUaZV5gSLfj9VJ2BABfTp+wsAKVlbU6VURz drbDM1O/QUSkH3z93A2WFA4ONug/oAOemzYYfv7uKC4Wo7TEdEpEu3YLx1vvTMKHi55BWLgvZ/Zv XZ0Ms6YvQWUlL53Qk+nZ8R/y+hJqdJJTeIl1c+p0CoRMB0fmJwBcu5oNFxcHdOwUpJNOt7a2hFRa j3ffioejky06dTJsGXILCyGi2wdgylMD8NigTrCzs0ZJscgovwb29jaYMLEvvvpmBmbPGcWL9Pfx 0fu/89WGUjKEmVQmSuIlJaKVmGNEUNxkSrCJ19snYPDLb/Mw8DHdfAFkMjlGDvsA+XllGDe+FxZ9 +hxsba2MhkSUUly5nI0D+y/gxPFrvEJ++v6CjRzdDf0HdICVleqT4A3rj+HTRRv4Hv5LWlb8y3wP 1lrAPTwo7kcQzONzrK2tFf7c9I7O8v/3/H0eb85fCQAICPTCD0tno32HQKN0IYqLRTh3NgXnzqTg woU05OeV6aUdDEMQGNQGMTHBiO0citjOIQgJ9WnRms7FC2l4/tnvoFDwKskttVJYRVzLXSHSO/m7 dJltUS1S/AOgD5/jvbxc8NeWd+Hj66YT6zl75jKcPHHtgXsx/40nEDdrhNEvwFWKa3HzZi5u3shF SkoucnNKkJ9XhrIyzSym2dhYwsfXDT4+bggL90VomA/Cw30RGuoDGw1+QYuKRHhy3Ke8200opqRm x6uURKbVkQ4NneXHsGwiAG8+xwcEeOLPTe/A3UP7IdCysiqMG/3xQ53bu08Uvl4cBy8vF5OLrEgk 9cjLK0NpiRhVlXWoqqpDVXUdqqvqGqwtCAQM7O1t4OhkCwcHGzg42MLDwwne3q46kWysqZFg2jOL ceN6Dj9jBmxNz4qfpOp9tG7mwgJn9CAMOQ7Amtfx4b74Y+NbPNNVW4bTJ69jVtzSh8oOHRxssPCd SZg8pX+rqEE2xJd09sylquQ+FbMM7ZCRkaByXrbWg+wV4sv57s6d00Awkc/LVlFejQuJaRg1prvW dfHbBniirk6K5KT/7yxZX6/AP8eu4Pq12+jeIwL29jZmRhou8VlKmSczsuLV2hhaJytM5eLkm+6u nS0A9Of1KheJcCExDSNGdoWlpYVW29azVztcu3obOY9UBd2+XYxtW07D3cMJke38zV8BLUMqrcdL s39USe6eEHyclrUmQd176mx5tVyU/I+ba2wwAF47PBcWVuDc2RQMH9EF1tbaq4RiGILHBnXC0SPJ EFU8nDMik8lx9HAyLiSmIbZzKJxd7M0s1YZ3UFGNF2f9qFKaNwH9OzUr4GXgODV48gNAYHC3PfVS tisAXlltJcVi/HPsCoYN7wI7O2uttcvKygIDBnbEnt3nIZHUN/g9P68Mf/15HBKJDF27hUMgYMyM 1RBybhdj+nPfIeVmrgpn0RssUz+6omJ5i1JTdUr+wsJLrK9l+50KS8Fg8MgAvW8VTpy4isFDY7Xq fzs62aJjpyDs25PYaGYly1IkXcrAwQOXEBjo1eqK8rUVcIh7/geUFKugFURQoIDFoMzMtS3WiBTo +oGLa6/KPbw67KBKZgwADz7niCpqsH/vBfTsFQkPLYZBff3cEdnOHwf2X2wygUokqsHuneeQcvMO OnQKhJOTnZnFKkKpZPHrir348P11kEpVKKihKGcZweDMzFUZmmiHQB8PX1Z2RdLGqdN2lpAxAHhl mNXWSvH37vNoF9UWAYHa2xw6KKgNQkK8cfhQUrPKa9lZRdi08QSk0np0igmBhYV5x0Y+yM8rw5zZ P2Ln9rOqKttVA2R4etbqy5pqi0BfnVAqvlzj6Nxlq4DQUXy/AHK5Avv2JMLN3Umr6QihYT6IbOeP w4eSmi0uUSpZXLqYjh3bz8DdzRHhEX7mqFAz2LXjHF56cTlybqvssVSyDEakZ8Wf12R7BPrsDLE4 qdbdPnYnBBgLgFdeA6UUx49dQXl5NXr3idLa5DM42BudYkJw6FASFHIl51fp8KEknDp5HYFBbeCr gxQNY0JWZiHemL8S8WsOqiPjWEGAYemZ8YmabpdBmKkI/zgfaoHDAKJUOa9jpyD8+PNcrRan3LiR gzkv/KjSpGzQ4E6YM3cMOsUEt2rS19ZK8cvyv7E24TDf5LRHXHykMxRjU7PjtSJ1YTDf6A5tX3KR CWV7APRW5TwXF3t8v/RF9OkbpbW2FRZWYM4LP+KWihLkMbHBmDZ9KIaP6KLTijV9QyaTY9PGE/jt 130t2aftDMvQ8eqkLRiF2/NflFRelPpatt+ksBTEguc6AHB3ZXDP3+ehkCvRtVs4GEbzbpCDgw3G PdEL+XllSFNBe7OoSISDBy5h+9bTkMsVCA7xNmnV6vp6BTZuOI7X5q3AgX0XUVcnU+s6FDTe0qZm cmrqhmptttfgZmfR0ZMs5RKHtQCeUvXc8Ag/fPvdTK3WBezacQ4fffA7pNJ61S2NgEGPnpF4Ynxv DB+p3ZVrXaKstBI7tp/F+nVHUVzcIsHbegK8nZoVv1QX7TbU0ASJCJ75FgX9StU2WllZYP4b4zE9 bpjWcvNvXM/BwjdXtWjjbWdne4we2x2Pj+uFjp2CjK6OQKlkceb0DWzbehqHDya1XHKFIIUl9NmM jIQknZHMkDs4IihuMggSKGCr6rldu4Xjk8+fQ2ioj9b82iXfb8e6hMO8FAWag7u7IwY81hEDH+uE vv2iDbaskmUprlzJwr49idi3J1FTBTIUFKus6+RvXC1eX6vL5zF4cxMePL0bCLMTFCqzWCgU4PkZ Q/DKq+O0RqiLF9Lw8QfrkZFRoJHrWVoK0a17OHr0jERs51B06BCo0QopVSES1SDx31s4ceIajh+7 qml5xTsMS1+4dTvhoF7cC2P4xN4LhW5RNRJ0H15eLnjn/ckYNbq7VtqnUCixLuEIfl6+G7W1mpUB FAgYREb6I7ZLCNp3CEJwcBsEBnpppaJKKq1HWmo+btzIwc0bObhyOQtpqfna2GNMSikW18qEXxcU rNTbnqdG42gOHLhIWJCb+wGAD6HmvgLde0TgzYUTEROrnfh7cbEIPyzejt27zrXYFeIzZwgM8kRA oBc8PJzh7GwHJyc7ODnZwsnZDg4Otv9xV9gHcif19QqUl1ehuFiMirIqlJZVorSkEoWFFSguEulC M3QPhHgtLS0+S9+cMrq1+IiQmSMopesAqJ1W2btPFBa8PRHR0QFaaWNmRiF+XLYTB/dfMtqdGbWA swA+TsuKP2IwURVj7MV7btAfAB5T9xoMQzByVDfMe22c1jbNuHolGyt+2YPjx65o/UtgwDhNKfNp evbqw4bWMCPOwlrEhIXkvkwovgJg15KXoGevdpg2fQgeG9RJKy3NzS3B+nVHsWXTyUaLZUwQ9QDd Qwh+Ts1MOGaojTT6FMTIwOmBLMOsAjCkpdeKjg7A8zOGYtSYblpJUS4vr8LWzaewbesZ5NwuNkXS 3yQgvysZNl6baQlm8j/yHOEhcS+AYjEAx5ZezMPTCROe7INxT/RuUiu+JaCU4tLFdGzbchr791+E RM00AANhUAFhsZWlbEL67bWXjavpJoQI/zgfKsTHIJgF9XeafAhhYT4YN743JkzsAzc3R423WSqt x7mzKTiw7yKOHb2Mqqo6Y+hqKQj+Zlmy3i/Af//x44sUxvnemiAiguO6UmAZ1FwXaAwWFkL06x+N QYNjMGBgR3h6OWu83QqFEv+eu4VjRy/j/LlbGls40yAuEZD1xFL+x61bv5cbvbtgunOuRUxYSO40 QvEleMol8u40QhAV1fZeSkJHdOiondycsrIqXDifivPnU3HhfCqysgr1EzUi2MIS5bsZGesyTYkh Jl9z19HrOTuJvcVcQrEQPMslVYWLiz1iO4cgJjbkbkpCxyCtpC5L6mS4desOUm7euSdGm4PMjEK1 MkxVmqMAE9Oz4reZGjdaTcFpR6/n7GR2lrMo6DsAtLobtkDAoF1UW8TGhqBL1zBEtQ+Av7+H1jI3 S4rFyMsrw53cUty5U4o7uaUoLhZBJKqBqKIGYnENZDL1tx1lGWWoqVn9VkX++4iOnmuvkErmUkrm a9odag42NpYIDfNFRKQfQkK84efvAT9/d/j4uGpclLe2VgpRRQ1KS8UoK6tCUZEI6an5uHY1G6mp eaqmMKSmZcVHmiIXWq3UQHT0JEtFneM4SuhsaGCNoKWTaVdXB7i5OTyQRBQIGNjZN65SR1n6IFeH UoqqqjrU1EhRVVmLqkYkx1vk8hC8mp4Zv9xMfhNFRHBcV0oxDwRTAFiZe+T/HpUCwvCsrJWVZvKb OEJDZ3gwSmYKAX2aEvRs5f1DQTA6LTN+v6k+oJn8TSA8PC6YKsnTBPRpULRrhdx/Ly0r4StTfkIz +Xl9EWbGCigdAxYjKUF3GJDqhRYgJ6CvpWYlrDD1cTWTX0VERk5zY+UWQ8HSkSAYDsDLhB4vERSz 07Ljr7SGsTSTv4X9Fxr6QpRAqexJCXoBtCdA2kFDeUU6AgvgBAX9IT0rYS+AVlN4YCa/hhEcPNuJ oYoeDKHRIAgGJSEAggEEwjAiSSKAFhDgCgtyipFjd+qd+ILWOFZm8usMi5iQkBxfCyIIYSkNAEvd CeBFCTwAuFNK7QkhDhRwJIANHi7QsQNwP19CCuBekB8iAKAEdQQQE1ARJURMWNz9G6ighC0CJfks oyyUSusK8vK2SMxjcRf/A18b/P13IK5VAAAAAElFTkSuQmCC "
       id="image108997-6-0"
       x="19.742722"
       y="486.65668" />
    <g
       id="g109577-0-9"
       transform="matrix(0.1714347,0,0,0.1714347,16.459508,510.78423)">
      <path
         d="m 79.797436,18.8 c -0.5,-1.6 -1.7,-2.9 -3.2,-3.7 l -30.5,-14.6 c -0.8,-0.4 -1.7,-0.5 -2.5,-0.5 -0.8,0 -1.7,0 -2.5,0.2 l -30.5,14.7 c -1.5000002,0.7 -2.6000002,2 -3.0000002,3.7 L 0.09743584,51.5 c -0.3,1.7 0.1,3.4 1.09999996,4.8 L 22.297436,82.4 c 1.2,1.2 2.9,2 4.6,2.1 h 33.6 c 1.8,0.2 3.5,-0.6 4.6,-2.1 l 21.1,-26.1 c 1,-1.4 1.4,-3.1 1.2,-4.8 z"
         id="path10349-4-7"
         style="fill:#326de6" />
      <path
         d="m 75.097436,50.2 v 0 c -0.1,0 -0.2,0 -0.2,-0.1 0,-0.1 -0.2,-0.1 -0.4,-0.1 -0.4,-0.1 -0.8,-0.1 -1.2,-0.1 -0.2,0 -0.4,0 -0.6,-0.1 h -0.1 c -1.1,-0.1 -2.3,-0.3 -3.4,-0.6 -0.3,-0.1 -0.6,-0.4 -0.7,-0.7 0.1,0 0,0 0,0 v 0 l -0.8,-0.2 c 0.4,-2.9 0.2,-5.9 -0.4,-8.8 -0.7,-2.9 -1.9,-5.7 -3.5,-8.2 l 0.6,-0.6 v 0 -0.1 c 0,-0.3 0.1,-0.7 0.3,-0.9 0.9,-0.8 1.8,-1.4 2.8,-2 v 0 c 0.2,-0.1 0.4,-0.2 0.6,-0.3 0.4,-0.2 0.7,-0.4 1.1,-0.6 0.1,-0.1 0.2,-0.1 0.3,-0.2 0.1,-0.1 0,-0.1 0,-0.2 v 0 c 0.9,-0.7 1.1,-1.9 0.4,-2.8 -0.3,-0.4 -0.9,-0.7 -1.4,-0.7 -0.5,0 -1,0.2 -1.4,0.5 v 0 l -0.1,0.1 c -0.1,0.1 -0.2,0.2 -0.3,0.2 -0.3,0.3 -0.6,0.6 -0.8,0.9 -0.1,0.2 -0.3,0.3 -0.4,0.4 v 0 c -0.7,0.8 -1.6,1.6 -2.5,2.2 -0.2,0.1 -0.4,0.2 -0.6,0.2 -0.1,0 -0.3,0 -0.4,-0.1 h -0.1 l -0.8,0.5 c -0.8,-0.8 -1.7,-1.6 -2.5,-2.4 -3.7,-2.9 -8.3,-4.7 -13,-5.2 l -0.1,-0.8 v 0 0.1 c -0.3,-0.2 -0.4,-0.5 -0.5,-0.8 0,-1.1 0,-2.2 0.2,-3.4 v -0.1 c 0,-0.2 0.1,-0.4 0.1,-0.6 0.1,-0.4 0.1,-0.8 0.2,-1.2 v -0.6 0 c 0.1,-1 -0.7,-2 -1.7,-2.1 -0.6,-0.1 -1.2,0.2 -1.7,0.7 -0.4,0.4 -0.6,0.9 -0.6,1.4 v 0 0.5 c 0,0.4 0.1,0.8 0.2,1.2 0.1,0.2 0.1,0.4 0.1,0.6 v 0.1 c 0.2,1.1 0.2,2.2 0.2,3.4 -0.1,0.3 -0.2,0.6 -0.5,0.8 v 0.2 0 l -0.1,0.8 c -1.1,0.1 -2.2,0.3 -3.4,0.5 -4.7,1 -9,3.5 -12.3,7 l -0.6,-0.4 h -0.1 c -0.1,0 -0.2,0.1 -0.4,0.1 -0.2,0 -0.4,-0.1 -0.6,-0.2 -0.9,-0.7 -1.8,-1.5 -2.5,-2.3 v 0 c -0.1,-0.2 -0.3,-0.3 -0.4,-0.4 -0.3,-0.3 -0.5,-0.6 -0.8,-0.9 -0.1,-0.1 -0.2,-0.1 -0.3,-0.2 -0.1,-0.1 -0.1,-0.1 -0.1,-0.1 v 0 c -0.4,-0.3 -0.9,-0.5 -1.4,-0.5 -0.6,0 -1.1,0.2 -1.4,0.7 -0.6,0.9 -0.4,2.1 0.4,2.8 v 0 c 0.1,0 0.1,0.1 0.1,0.1 0,0 0.2,0.2 0.3,0.2 0.3,0.2 0.7,0.4 1.1,0.6 0.2,0.1 0.4,0.2 0.6,0.3 v 0 c 1,0.6 2,1.2 2.8,2 0.2,0.2 0.4,0.6 0.3,0.9 v -0.1 0 l 0.6,0.6 c -0.1,0.2 -0.2,0.3 -0.3,0.5 -3.1,4.9 -4.4,10.7 -3.5,16.4 l -0.8,0.2 v 0 c 0,0.1 -0.1,0.1 -0.1,0.1 -0.1,0.3 -0.4,0.5 -0.7,0.7 -1.1,0.3 -2.2,0.5 -3.4,0.6 v 0 c -0.2,0 -0.4,0 -0.6,0.1 -0.4,0 -0.8,0.1 -1.2,0.1 -0.1,0 -0.2,0.1 -0.4,0.1 -0.1,0 -0.1,0 -0.2,0.1 v 0 c -1.1,0.2 -1.8,1.2 -1.6,2.3 0,0 0,0 0,0 0.2,0.9 1.1,1.5 2,1.4 0.2,0 0.3,0 0.5,-0.1 v 0 c 0.1,0 0.1,0 0.1,-0.1 0,-0.1 0.3,0 0.4,0 0.4,-0.1 0.8,-0.3 1.1,-0.4 0.2,-0.1 0.4,-0.2 0.6,-0.2 h 0.1 c 1.1,-0.4 2.1,-0.7 3.3,-0.9 h 0.1 c 0.3,0 0.6,0.1 0.8,0.3 0.1,0 0.1,0.1 0.1,0.1 v 0 l 0.9,-0.1 c 1.5,4.6 4.3,8.7 8.2,11.7 0.9,0.7 1.7,1.3 2.7,1.8 l -0.5,0.7 v 0 c 0,0.1 0.1,0.1 0.1,0.1 0.2,0.3 0.2,0.7 0.1,1 -0.4,1 -1,2 -1.6,2.9 v 0.1 c -0.1,0.2 -0.2,0.3 -0.4,0.5 -0.2,0.2 -0.4,0.6 -0.7,1 -0.1,0.1 -0.1,0.2 -0.2,0.3 0,0 0,0.1 -0.1,0.1 v 0 c -0.5,1 -0.1,2.2 0.8,2.7 0.2,0.1 0.5,0.2 0.7,0.2 0.8,0 1.5,-0.5 1.9,-1.2 v 0 c 0,0 0,-0.1 0.1,-0.1 0,-0.1 0.1,-0.2 0.2,-0.3 0.1,-0.4 0.3,-0.7 0.4,-1.1 l 0.2,-0.6 v 0 c 0.3,-1.1 0.8,-2.1 1.3,-3.1 0.2,-0.3 0.5,-0.5 0.8,-0.6 0.1,0 0.1,0 0.1,-0.1 v 0 l 0.4,-0.8 c 2.8,1.1 5.7,1.6 8.7,1.6 1.8,0 3.6,-0.2 5.4,-0.7 1.1,-0.2 2.2,-0.6 3.2,-0.9 l 0.4,0.7 v 0 c 0.1,0 0.1,0 0.1,0.1 0.3,0.1 0.6,0.3 0.8,0.6 0.5,1 1,2 1.3,3.1 v 0.1 l 0.2,0.6 c 0.1,0.4 0.2,0.8 0.4,1.1 0.1,0.1 0.1,0.2 0.2,0.3 0,0 0,0.1 0.1,0.1 v 0 c 0.4,0.7 1.1,1.2 1.9,1.2 0.3,0 0.5,-0.1 0.8,-0.2 0.4,-0.2 0.8,-0.6 0.9,-1.1 0.1,-0.5 0.1,-1 -0.1,-1.5 v 0 c 0,-0.1 -0.1,-0.1 -0.1,-0.1 0,-0.1 -0.1,-0.2 -0.2,-0.3 -0.2,-0.4 -0.4,-0.7 -0.7,-1 -0.1,-0.2 -0.2,-0.3 -0.4,-0.5 v -0.2 c -0.7,-0.9 -1.2,-1.9 -1.6,-2.9 -0.1,-0.3 -0.1,-0.7 0.1,-1 0,-0.1 0.1,-0.1 0.1,-0.1 v 0 l -0.3,-0.8 c 5.1,-3.1 9,-7.9 10.8,-13.6 l 0.8,0.1 v 0 c 0.1,0 0.1,-0.1 0.1,-0.1 0.2,-0.2 0.5,-0.3 0.8,-0.3 h 0.1 c 1.1,0.2 2.2,0.5 3.2,0.9 h 0.1 c 0.2,0.1 0.4,0.2 0.6,0.2 0.4,0.2 0.7,0.4 1.1,0.5 0.1,0 0.2,0.1 0.4,0.1 0.1,0 0.1,0 0.2,0.1 v 0 c 0.2,0.1 0.3,0.1 0.5,0.1 0.9,0 1.7,-0.6 2,-1.4 -0.1,-1.1 -0.9,-1.9 -1.8,-2.1 z m -28.9,-3.1 -2.7,1.3 -2.7,-1.3 -0.7,-2.9 1.9,-2.4 h 3 l 1.9,2.4 z m 16.3,-6.5 c 0.5,2.1 0.6,4.2 0.4,6.3 l -9.5,-2.7 v 0 c -0.9,-0.2 -1.4,-1.1 -1.2,-2 0.1,-0.3 0.2,-0.5 0.4,-0.7 l 7.5,-6.8 c 1.1,1.8 1.9,3.8 2.4,5.9 z m -5.4,-9.6 -8.2,5.8 c -0.7,0.4 -1.7,0.3 -2.2,-0.4 -0.2,-0.2 -0.3,-0.4 -0.3,-0.7 l -0.6,-10.1 c 4.4,0.5 8.3,2.4 11.3,5.4 z m -18.1,-5.1 2,-0.4 -0.5,10 v 0 c 0,0.9 -0.8,1.6 -1.7,1.6 -0.3,0 -0.5,-0.1 -0.8,-0.2 l -8.3,-5.9 c 2.6,-2.5 5.8,-4.3 9.3,-5.1 z m -12.2,8.8 7.4,6.6 v 0 c 0.7,0.6 0.8,1.6 0.2,2.3 -0.2,0.3 -0.4,0.4 -0.8,0.5 l -9.7,2.8 c -0.3,-4.2 0.7,-8.5 2.9,-12.2 z m -1.7,16.9 9.9,-1.7 c 0.8,0 1.6,0.5 1.7,1.3 0.1,0.3 0.1,0.7 -0.1,1 v 0 l -3.8,9.2 c -3.5,-2.3 -6.3,-5.8 -7.7,-9.8 z m 22.7,12.4 c -1.4,0.3 -2.8,0.5 -4.3,0.5 -2.1,0 -4.3,-0.4 -6.3,-1 l 4.9,-8.9 c 0.5,-0.6 1.3,-0.8 2,-0.4 0.3,0.2 0.5,0.4 0.8,0.7 v 0 l 4.8,8.7 c -0.6,0.1 -1.2,0.2 -1.9,0.4 z m 12.2,-8.7 c -1.5,2.4 -3.6,4.5 -6,6 l -3.9,-9.4 c -0.2,-0.8 0.2,-1.6 0.9,-1.9 0.3,-0.1 0.6,-0.2 0.9,-0.2 l 10,1.7 c -0.5,1.4 -1.1,2.7 -1.9,3.8 z"
         id="path10351-8-9"
         style="fill:#ffffff" />
    </g>
    <path
       d="m 19.676908,529.51433 v 11.99169 H 7.6852478 v -11.99169 z m -5.99583,2.76732 c -1.07139,0 -1.98432,0.67843 -2.33262,1.62912 v 0 l -0.0963,1.7e-4 c -1.20578,0 -2.1832702,0.97749 -2.1832702,2.18327 0,1.20578 0.9774902,2.18328 2.1832702,2.18328 v 0 h 4.85784 c 1.20578,0 2.18325,-0.9775 2.18325,-2.18328 0,-1.20578 -0.97747,-2.18327 -2.18325,-2.18327 v 0 l -0.0963,-1.7e-4 c -0.34831,-0.95069 -1.26124,-1.62912 -2.33263,-1.62912 z"
       fill="#4285f4"
       id="path119683-8-9"
       style="fill-rule:evenodd;stroke:none;stroke-width:0.599584" />
    <rect
       style="fill:#000000;fill-opacity:0.02;fill-rule:evenodd;stroke:#000000;stroke-width:1.29494;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="rect117-9-3"
       width="194.16092"
       height="155.75706"
       x="230.85725"
       y="388.21417" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,325.29258,445.45562)"
       id="text4916-3-2"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-9-2);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="40.033203"
         y="354.32031"
         id="tspan212425">Your cloud stack</tspan></text>
    <rect
       style="fill:#2080ff;fill-opacity:0.0866484;fill-rule:evenodd;stroke:#000000;stroke-width:0.967304;stroke-miterlimit:4;stroke-dasharray:0.967304, 1.93461;stroke-dashoffset:0;stroke-opacity:1"
       id="rect117-6-6-4"
       width="177.61057"
       height="95.010628"
       x="239.14857"
       y="432.54843" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,303.0066,428.66448)"
       id="text4916-5-8-3"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-7-7-9);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="40.033203"
         y="354.32031"
         id="tspan212427">Kubernetes Cluster</tspan></text>
    <rect
       style="fill:#262362;fill-opacity:0.119759;stroke:#000000;stroke-width:0.803943;stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:0.803943, 1.60789;stroke-dashoffset:0;stroke-opacity:1"
       id="rect31353-0-7"
       width="166.26421"
       height="68.42569"
       x="243.47853"
       y="440.4537" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,278.81273,411.26291)"
       id="text4916-5-1-21-1"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-7-9-7-0);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="40.033203"
         y="354.32031"
         id="tspan212429">Sandboxed containers</tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,54.850218,411.45785)"
       id="text4916-5-1-21-4-2"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect4918-7-9-7-3-9);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="40.033203"
         y="354.32031"
         id="tspan212431">Sandboxed containers</tspan></text>
    <ellipse
       style="fill:#fe0000;fill-opacity:0.213973;stroke:#000000;stroke-width:0.999997;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path15701-0-2"
       cx="298.6922"
       cy="364.13855"
       rx="46.290905"
       ry="15.684086" />
    <ellipse
       style="fill:#ffa700;fill-opacity:0.190454;stroke:#000000;stroke-width:0.859101;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-5-0"
       cx="269.20349"
       cy="410.78934"
       rx="28.393295"
       ry="13.157152" />
    <ellipse
       style="fill:#e6fae0;fill-opacity:1;stroke:#000000;stroke-width:0.803217;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-1-2"
       cx="275.41821"
       cy="472.08548"
       rx="24.766687"
       ry="13.185097" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,209.30826,279.65188)"
       id="text22680-108-1"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-64-9);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="146.69196"
         y="488.62894"
         id="tspan212435"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212433">Hosted load </tspan></tspan><tspan
         x="168.47584"
         y="521.96231"
         id="tspan212439"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212437">balancer</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,229.67491,229.06917)"
       id="text22680-10-5-7"
       style="font-style:normal;font-weight:normal;font-size:42.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-6-3-4);fill:#000000;fill-opacity:1;stroke:none"
       x="171.41656"
       y="0"><tspan
         x="176.29519"
         y="502.78518"
         id="tspan212443"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212441">Outside </tspan></tspan><tspan
         x="199.94104"
         y="556.11856"
         id="tspan212447"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212445">world</tspan></tspan></text>
    <ellipse
       style="fill:#39c681;fill-opacity:0.210933;stroke:#000000;stroke-width:0.859101;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-0-0-5"
       cx="328.88293"
       cy="410.64178"
       rx="28.393295"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,268.98774,279.50437)"
       id="text22680-6-6-1"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-36-0-5);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="138.57997"
         y="488.62894"
         id="tspan212451"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212449">Hosted static </tspan></tspan><tspan
         x="152.76618"
         y="521.96231"
         id="tspan212455"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212453">file serving</tspan></tspan></text>
    <ellipse
       style="fill:#fff8ba;fill-opacity:0.753457;stroke:#000000;stroke-width:0.859101;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-6-4-7"
       cx="387.62048"
       cy="411.17093"
       rx="28.393295"
       ry="13.157152" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,327.72522,280.03354)"
       id="text22680-1-6-4"
       style="font-style:normal;font-weight:normal;font-size:26.6667px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect22682-2-3-1);fill:#000000;fill-opacity:1;stroke:none"
       x="62.83844"
       y="0"><tspan
         x="179.42637"
         y="488.62894"
         id="tspan212459"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212457">Hosted </tspan></tspan><tspan
         x="164.63469"
         y="521.96231"
         id="tspan212463"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212461">database</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,207.72078,269.59771)"
       id="text45097-2-1"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-0-0);fill:#000000;fill-opacity:1;stroke:none"
       x="42.939453"
       y="0"><tspan
         x="218.94336"
         y="758.01953"
         id="tspan212467"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212465">Web 
</tspan></tspan><tspan
         x="208.26367"
         y="798.01953"
         id="tspan212471"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212469">server</tspan></tspan></text>
    <ellipse
       style="fill:#cdf8ff;fill-opacity:0.681529;stroke:#000000;stroke-width:0.786135;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-7-58-7"
       cx="328.12756"
       cy="471.55634"
       rx="23.709324"
       ry="13.193636" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,259.48648,268.71167)"
       id="text45097-9-6-1"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-8-9-3);fill:#000000;fill-opacity:1;stroke:none"
       x="42.939453"
       y="0"><tspan
         x="228.51367"
         y="758.01953"
         id="tspan212475"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212473">API 
</tspan></tspan><tspan
         x="208.26367"
         y="798.01953"
         id="tspan212479"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212477">server</tspan></tspan></text>
    <ellipse
       style="fill:#f1dff7;fill-opacity:1;stroke:#000000;stroke-width:0.7787;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       id="path19868-2-7-0-2-1"
       cx="381.09412"
       cy="471.59494"
       rx="23.256241"
       ry="13.197355" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,312.90973,268.75026)"
       id="text45097-9-2-84-1"
       style="font-style:normal;font-weight:normal;font-size:32px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect45099-8-2-2-7);fill:#000000;fill-opacity:1;stroke:none"
       x="104.11133"
       y="0"><tspan
         x="220.70898"
         y="758.01953"
         id="tspan212483"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212481">Data
</tspan></tspan><tspan
         x="195.43555"
         y="798.01953"
         id="tspan212487"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212485">pipeline</tspan></tspan></text>
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,223.06662,305.05189)"
       id="text62482-7-7"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect62484-5-8);fill:#000000;fill-opacity:1;stroke:none"><tspan
         x="33.445312"
         y="978.39258"
         id="tspan212489">Security:        ★★★★★
</tspan><tspan
         x="33.445312"
         y="1028.3926"
         id="tspan212491">Performance: ★★★★☆
</tspan></text>
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-4-8)"
       d="m 269.20351,423.94649 6.2147,34.9539"
       id="path80708-2-0"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-4-8)"
       d="m 381.09412,458.39759 6.5264,-34.0695"
       id="path80708-7-4-4"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-4-8)"
       d="m 328.12757,458.36269 59.49295,-34.0346"
       id="path80708-7-1-0-0"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#ExperimentalArrow-4-8)"
       d="m 275.41821,458.90039 112.20231,-34.5723"
       id="path80708-7-1-3-62-8"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-47-0)"
       d="m 269.20351,423.94649 58.92406,34.4162"
       id="path80708-7-1-3-4-9-5"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-7-54-6)"
       d="m 298.6922,379.82264 -29.48869,17.80955"
       id="path80708-7-1-3-4-4-9-1"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#000000;stroke-width:0.5;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:3;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker-end:url(#Arrow2Lend-8-3-2-7-5-1-6)"
       d="m 298.6922,379.82264 30.19079,17.66198"
       id="path80708-7-1-3-4-4-3-0-6"
       sodipodi:nodetypes="cc" />
    <image
       width="19.167257"
       height="20.873243"
       preserveAspectRatio="none"
       xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAL8AAADQCAYAAABWSLCJAAApKHpUWHRSYXcgcHJvZmlsZSB0eXBl IGV4aWYAAHjapZxZklw5b4XfuQovgfOwHIJDhHfg5fs7zJS6W9L/0LYUUpWqsm7yEsAZQFy58z// fd1/8Wvk2l0urddRq+dXHnnEySfdf37N93fw+f39ftn9fi/88+tuf7/uI19KfEyff/b6ff2Pr4ef F/h8mHxW/nahvr7fsH9+Y+Tv9fsvF/q+UdKKIp98V+LG90Ipfr4RvheYn9vydfT2j1s7n48/76R/ /jj9ZT++Wr4v/uXfubF7u/A+KcaTQvL8ndJ3AUl/okuTT/r7u/DCkBqfZ76lr/xYCRvyp336+Wuw oqul5j++6B9ROfbnaP34zP0arRy/L0m/bHL9+fGPX3eh/Dkqb+v/9s65fz+L//z6PDF9VvTL7uvP vbvfd8/cxcyVra7fm/pxK+8zXscNZ12oO5ZWfeNP4RLt/R787mT1IhW2X974vcIIkXDdkMMOM9xw 3scVFkvM8bjY+CTGFdP7Yk8tjriS4pf1O9zY0kibyMa0XtiJ6c+1hPe2wy/33q3zzjvw0hi4WFBe /Nvf7t/+wL0qhRD8d/NJC9YVozabZShy+puXEZFwv5ta3gb/+P3rL8U1EcGiXVaJDDbWPpewEv5C gvQCnXhh4eOnBkPb3wuwRbx1YTEhEQGiFlIJNfgWYwuBjewEaLL0mHI0IhBKiZtFxpxSJTY96q35 kRbeS2OJfNnxdcCMSJRUqbNOhCbByrmQPy13cmiWVHIppZZWehll1lRzLbXWVgWKs6WWXSutttZ6 G2321HMvvfbWex99jjgSoFlGHW30McacvOfkypOfnrxgTouWLFtxVq1Zt2FzkT4rr7LqaquvseaO O23wY9fddt9jzxMOqXTyKaeedvoZZ15S7SZ38y233nb7HXf+jFr4lu2vv/9F1MI3avFFSi9sP6PG V1v7cYkgOCmKGQGLLgci3hQCEjoqZr6HnKMip5j5EamKEllkUcx2UMSIYD4hlht+xM7FT0QVuf9X 3FzL/4hb/L9Gzil0/zJyv8ftT1HboqH1IvapQm2qT1Qf3z99xj5Fdr99dHEeVjR2XMesj915h0q+ wJD3gGwlTUuetay8eyQSZZ0U9yp1rVCL38YKSiNq12drsc7FRgJoY9fKp4n177BA+hHzaWn1ckap JwGXtd/EBoZ1WWezMe1Wc+xhaJYml9/eFvvWiA8/Xs3CTumEdLuJ8/ywyOJL2ueEzi7kuS+x0BcW wNZz7jUSIbC2zplvY1tKLG3ePUCxYhf+S63NlYu/k5cks1v2LnN7XpBRDfc4s0K86r1kZGl39JTt WMijEvzSLyIg2r4E0QNFvNkh8OO0uwmPQtD77KdcB1xPAnNtbNJ3Z32T9Xt9jCXoqiWTFa0Hi72W OBcBFVIUn/Yo7MrZyU/HZRustZu1zRv7NHwc1fJunt3Ze0S/eP+w+jzVeC945sRhgOMM/QSb+exT kgt7tfve/9guzSrlsBFLFg43NqmURY20Wvgp38qcJZ4xWgqHAvCQ6KK6qk03t0SL3R1ru5H1Gtuz D0l0VmfX8xns5s2H5OrxtLgo85iDEJvsIii5pT2zSwTpkEFs/GVRVCS6BpIGs1cnVMSFtIrsE/We WHAyaU9l9Kmhs4tF2qy4H5/8p4/ppD7zGFZXVRSNVN9nk0vcbZjsF8Es9bpt22axLG5pBG1uEhJh pUK5MEzNqd5ZyXne/65zQZ/FdoVbpykZAQZqfHJrIxRSI2fK7Uaqm8RISuxtd4VyfZ17bijp2sq5 LVLtRm7RVoR2be9F2k4D/EkNEiDVACyMCi6lRW7dRvmWfM3vQWX5WyjpxlfInhZJ+xMGS/ZEvCFb tgtnQX02jULhFshbMJqfA1woicZ7ANul7hAXyF1B32rUDdlAvhmgyP6AlcdRGq0kMFiCyKjy3T8l TdYdUoKkqj2Rfwe4KkDnIOuH1g8nWKZUQB4AyZFAIFILg6ohTws7UQ0cY4u82Z4NiN9r6ofJkNIX So78a4IT6z3GvcFpbq2FBBwtQBxwZ5Gef2oTNpU7MgW+alnKmjjzb8jY2QqweN7pXSVdp++HYjcQ olNAgZLbbbDdvR3PdwLYAVSB9cB2POQ5KH2EVPn0dOO8uTneaSZ4op0AXxJHNubCKABXL3sMEm/n tSs3B4XximXgRwuCj1OinU2Nx35c68DIKKFQqR2MwJOwGq61E6vZcBYFw8sB57NGg84AFba4GFxA 9O+gguqObhWSAgKqVFxi/QAe+D59yYOFAvQAJUGv7OGBEBawjTehKHcXxFVYaYDV0ZWx71PBBxcE lcF12kHAYlLUm9I4fYP7rZjKhrxIZKbeY8Zx0WKQR4ehXSCCYxSWk3cyuP8Tkexf1Rb/10d0GznB 7aVYeUcKcwAsA1UMPy43X64CtDaxA71CkMXAVUzVxEWmUyPAQcGDGBci4pYgOCOdD7gFLKHEgQhz Al1ysV1eF6A3YC1RF16QdPepYslwZgb/Zqyd7KPMJqUMsICRs4Iw0NZxsDdwCKSwVMXpAiYIiOf1 pAkymev31Y1zvbW5cNlWCOYs0AEEDHoAE46Uwx9QwLfWtGZGdlwwOfASXMAAktgI1AWktIwkA0gy iZRjGbzXBrq1hNwd6gNFRE22S961AUTPuWytCSeye0gpBMv1xLMCWHuSDiQIeoxyva2m3QtojYgg 7CeSqBN6hEu20NOPcDAn5OhtpHNkyx7Ps0+NS7MY4Tvc1RIiZ/NtcwQkD7i0T6tn2ViWzwLkM1BK LVKGETzseCyypfSDX1J5A0cgtaHxZ4FvWnPnsBu9wXKp7gQxwV/rIENIs42j7oUAkw0VcRZt9Ppb fvGxhOV2WlwhID8A78r77TLAdnUzhiinnEee+IteIKSYQdO1jW1lixGkq0toVO/KZkvJC7A8Izu4 rn4wI/KkKCbEP0k+NDIVEwqS795AUaFu7qPczD/Y7OtgaCLakGx+rLBjBMPB6xTgCQLChnFD0Ho3 UsQm5GCWL5bp5IyWm7wyw+XLsT/HKEpSjsixrjH77QhLdEDmUgi7Cl/HASHwGSBKPZxdzoI/Vov6 G5wfru7Ky0haIPGg01PBXKyLUV7USl4DVLyFYIG4laRmt1DZCVQlo21HWGQYis5Nn8T9pt0wFkNV +JXOsUZipR84USQnu5CJwsrgGLxiqtpD1ksFJKdNW0Y1EFgpiF3x3qOxZnWOWBjymhQDlgvif5Gl VNfAZaABCAnAjPKeJbs7w0SLjwV4tn+iUZFcoQaHSuJKDrRABUF4a+zhAwagUv7onIQ+QkBTMlR8 kUJGO1K5SMgMgakVADVgAGJlr1+nIUtZoNoooZWBsQ8fU7uukGpAPJCXOkmHABYyoUmm4gUFYKyS IUJYjSeGIP9McfF+FPRmH8XQa2/HW7QNI4BgQ0ATiop4sUNcskPK3vrOs0Vv5BiGOd1VoelGhNjj xn3a2gNPC4Tjral4dCZ3165hdSUzJrrHK01Bksi/bvsqzSZ6D2nFxCpRT8GbH46MRSsFAMgIxm0U CmproGAH5gQwoyTUbqgwPf6uiXElGpZeemRbB3HF9x+PlmLjIdrr+UKVzkJN+TYGe5MGwPlwSg6l YrtIWvb0LBgSgJZEo9brcMi1bU/3pBC7fKOgBjIjgZ/i9H9TnHzED5VbIKHt8S/nNu4GqxgdMhdZ FKifJYPnCWhlu1BpYaRNwSb2XTGWGEf5l8GWn6w7g2myXB35s70T+wLgRzVr3D6iH8kgPk0kOjAh ykWY6Pa6cgh6b4MwQ8ixoLqhzJjGcdAlSYjuwLhBgyyurEcD6OJKxSEbQSKkBcAJV8L9Sqx7VNOX Ak9rY2YbeZR4g7XuRMQsg4ba5d8ErSmLu9C9sysslFKy1QJUN/DplOXFzyxAmlzArnMRD1mgGngJ KAS8b93GPOhDVGEvasFwT3hqLgAZYiEEryYkghHIkYOlc+RoRYXMCcMow0GKmcFAwOCI0oratYAD ygngy5U8QyFAHSg3q3gfwGOTJC4ONCb7mXACJB1sxcK8wXZk4I0t8+/CDVxUCMKfJUI3mMCMaYXs 7MKesPhwiFg4kh9jc9FTUl+Sl+wkAMC1F76uHbZ1AXcmkQ5hqR2B46+kLU6SVZ3gsrxZShvUe64N xxwBH0yAZys61I3hQN8ATBnaE2ZTQuAK2IdbhgvRh4OoJdReUgxAMfYQMe1x/hhoyGLxzrAT5Msa MAbIZrhhVZh7asdBnxATyLiuAWxk6sD4y3cahlS4AtnZQU0q2hQp1BC3WpRc7WQ8Izke/FJUb0Q5 YVwxNVAZih4sByQh5d4xACNTowDx0Y4etX9YGvS62akqDcTu14DSJQtFm7gEB517kL3y2sR9BMlZ zAuJ7KWwgEOkgJA5YW4Rp9zAZt99aPi3CrxREAspKLu+kMM5e/w4EhF9sfDAZ0jhTJjaI51V38Dn wP1V8BfoWFumG1fGW3gSb7uVoRYiH3WN6yl5UGLVISEZSMCBxZKV66Gq2xU7ogqNpFJP46KQwPpw mjnBPCQK4/NCKgvDQYqSy9w5cpWUH7qdUtQRDQ9coPd6l/ZBnSd5jrYTCEnEKADUAmpRwabGSOWj KyGQwAH0CjgRFECkVIisHnBGeu5PU6Ogt65jp0eOSNMD/IAooVsyWQbU4QK2KWM4BDnJGyHQuXus KlWAcQwN3FkLqMddOrIC/MG+cWlACAZqpAxMQ2bKRgUpbVVGqzfhNQM7c0GSgGM00trAtoyid8Bf JP+fjkPo++fx2NGIDJahwQU09SHgEFbMqg0bSXagwuFkDAGIc4OZQ6Gjz9Hh0B+eAVMcO6kfd8Jb E0g2S63nqM6dyhykuHV7UDUu0WjCK/L2CC2MMTcDrRDlkrC9ZRJPtJEyOKr+Cc0RTaN0cF0TlpLg 6Q3eJ2igEZuG9Mu1E4ona9UpQP+chDqHPZAFltEQOMipxqMZtxERUohOTDbcf0WQoaH+irvnGhZq QNToiJXYUxiCF++0BfjUCdsIL+JrlGyb2uIuN3eFTiw4hiSVaw6ul00nM0oYyAeYJ8140GFZO3fU zIJ3cQ61CUU9m9QhDzCIHEAzo9SQVovN1gLrGgoOyE20GjsT2qFUC9R/hloDYMNWzgJbWRdiY7E2 FB2B3dLCbvPSDdyUqu/oxhdSLD8m8VRwljsryaAVnCssAj29vTkD2UPMwueVjoC/nth7g/YuA0zp mzDOkw9Lq5dW/SxowQ+HbNmYuhk2VadQT8d7wgNwKpli+GOYBTUwKAdgnXo6Jn7skCEOkTig0gUo YNnnbcBc4jPhfhKYZF+kD2WtOGCB12ow1ohaQWkI4mbqaKtTB6U9pwt2ywmjJ9A42D9HGm3xa/YK MeXjxyaPNvuIGLU8UGLkmMovYDrUM6k4JXAqSBjug6QzAMmBlCgH2YyiT7h3EhOiCBAXthbiYgk4 vg2KoooaZJxxJjAnVi2wT1gmI6ccFg/Ske4iemyNSAdEf6Kqjb71a2RV46oUG2lKAe6kji9oHs+h mtjBhKpVE3BgK6kLBAKqAWvTSEmcAvqHeIJvwxpbMUX/2Fm1ZKrkNW4GcG1yIQ5BvBAtHgKgUA7+ MRc2h5JUv6kO5NDAxyQqGqFiJxA+JAb2v3mMWwsXlQ3Pu4KKNXEMts2iahGyHY1IDAAopeEhDxb8 6RYuwA0Ir+vrSHP3JVVgLrqI/YF2Z/QHqAqIJXQUhI/SRbD0pWMTgDc2lTbZBKbX26F49btvxpRX 5O/oDobv4LHqGCtAUNjKaJRZHqTP86B4j4FQRrQgLXF55B/CTxpv8UPoisFdONGZytLYqafsSUt+ AFAlTbAR5AosSAbzBer9KVG1rXCHOuFkX4ZOrSeUzc8F3AeWvwtaVqQwSMaKd44FAQfREbaI0bxs PrmOGEVuk7z83edWf1jHGRhZ/wPyf0J/RhaTmCQNdwnUkl8QCEyd1dvDq3f/evF5ItkF5dWhcGUQ 0A0ScmqZePAeroO8ok5pJD0oYL5aiTUrJObQM0hOHp0lwsICFBdQ5Qme2QvUIWQ4jdLlcltl79Bs OjWCvAPwQ/WDTxCeSUUWNuD4D+CA2Q1XDmJGIrsApaXfAXAND9rQxmQcwrgfNtNTDpgQlIDp4Gki l5u0V0QDwbQdxY13mUAQthau7uoRRgkt9aupGgAJrMGPyI5nnPQ9OjFVpwKEQqd5pB/gBup4rSXJ e6jvr5h6ZO481EbC7bamYx51MUF7hCh7AkHark3UBPGO7Y5OqxDebCGwhM4GNxHQauyX/imNw65i 2yB52RbtqKSMpd7UBWABO6oRZTp5qE0nE2y8byxXpx8RIIyyLiXiUTw0b2ywjn2vdg3HuUuOZPfz d9VL1sAc3bcKSFDqGNoMGcfD7e6kbhBek7yEYEbCKUtGH6or51hVMuwp1pALSY6js0EpsdWimDZ1 oBBnQJE7ruQ7VVX4FAPIlyLs/Y5rGnoORoWdsdfUGjYHj0H+E8tBRFHIU70AMAX46ve1CPBPr/Pz 6pE3wbp1eKPArIbMidGZOvM44SOdNXRGyMqJVQF48KIZ01l1yowWsYsFmAfZgiElUuWNl6DS0NvX wZz8cEZcCkCUfmk0QnyIWkQJjdc2z89eSPaypWeiTngjj2EAn183BX3k2djXvZY4BbXRXdAFojFT UCQ7OT4VcTUUyL8DL3sSUtoWMNGpaKQYBi6bGAq9pMh0pgaBNUSezr8xPFnnGF9okPMWrGA8x/Sk aFRXGt0AvCZMDf5iAThcLWCvPCCDraAudEYCeXisZGpRDlMLs9dx0KwHOHsVWtn3eRzmnfCvqD4b zAKBy5Jm0ol3ACMHcoz3GC8bIV2WHdU8xocEP2X/eOOL0EJsH3VPUEeQDSpLh15xXNUBmuJDFg2m ++2kC08BQcPflOtFsHuYdLB9QOfpqQMVHgMwJ+sksxCuzy7ID6AvcbW8qlq56iCjzXBEmDQqAQep pT17KKxoa8LLqNnXG5MwE/yhDSlutlndOZ1nQB9oTvW2dKqJuCtUvzrNE394BeTcLrmC0U5P2p9u ePNWF5oG79HQj1cnA6UhKe4YSy4oSBjCIlh5EFbNYfTTM/4e9CNbQfUE4aGi7iG7dWYOoRW1wIKQ aqJQmwp+h5oQ7GMOncY2rDq3IpFaDanOaljkw+QbI2rkkQQimOuzTnRvU48+If4RG90pv6/OxCQ2 5R2R97OBvDphhoTV8CIcRNUIVFRjRZ2IKSeu1g2CABicFaj1cssNj1rJQ7IVNJUUoVyg1ammaqpA yDk6+78XyQBwnJ5l5PCtQGAfMVO0HUEjNi6YAeQ5CK0etJA4q7XHTwIHsHKhpNGfhJm/MxZzj86O Tfa4jMe08ugVxlM3H5RUxmSMGH/vBvrqjL08Xh1PU+uI/X3ElbOvpvNujxjtCqV2NSP4WEHAWUGl C3x55GdiD0Q72arOIXr7as6iaUrHf1oBJB0iYqtd0FJVjSWwQ0YppEMI1aCGTWW1l0yN6CSEvN55 Gmg6o47TTlIDoLgaBoSrZhcpGcCujkNtISIgzXTkBx7DwIS66xD54nnBJ0pcB6bDqnz0rSW4rfYK DPWacVQI2aUOWWBxuB6SG0F+NdvR7C04dcKJGqAm7bXOT6cm93a9IF9Oa7lz6dpzizqKVpMBfU19 A8GKLlyXv2ZIQ2gQSQbdcCOQ5m5E2iFCEI0DMsiU58cjn/rz9CPvqq4iBrpgLfOnsYw7qKQ0tQTH fPIQGIm4rSQnPi08A0VsfE8fK6XenI861Snq0kk0eMTH4D7zUYpMtUJLtexAwP0BrzXGQW8jq2Gy Wzw7gkjPT/ck4Q/vQOE03C7Qw1cQvNsoRna9XIcWw6fgwgJesm0l+glLLT4N6+FKM2Q3ravXRgYi znVIC6W0z7EUmcOya3Avy0ELCL+Uc8mmoJ4LPE1JDcAYSWQwT3n4pFYYuZGRKCRgl4/AtuE/zE3p AsQsdv7iN9iroJXV54gRwXj4SDU1+/SQ1YzIOq6QsEE7IxJQSATdgeo691IZaSDN2DidDyZU0hoo gk7WZR0PRSUSUIIeEnujAjHZWe2cCM1cN6jkZqzE2EhITocOOqNPsPhW18w8FJeS3zOjyY4GFDQk Q/x00I67ByA0E0FpUQGas6u7RhQlNRk36C62fFxPrdlMoUU1nDEb345M48W4H8Qb7926OhGCUE2r CBcKlonAIkfTUh/5aIzi4QnbsyTlQojfvNUQ39WBHL484NcyYckR4kNkZI+WRnpBTgFlJ2Fhw7pO 8TwFl1UzuFKKA122hNVz2ERhTo1WmU6RALZl+40EoFxGADGqRjbQI+yqcf/4jjY0psE+k7oZjFns Aixa5UWyiBaZSwJdnZSXiSbSyV7POpxcEONtyXQadSURCLFaIVkthB1xupqX6IeEPJSvgH6od575 IbiYIkX9kdTrew4r1zWmhgUyrErM53l7O9WvbdxxdWNR+gfPQsI+7XVz1lEODl4IeUzHIYRdqqB0 Ag4u653I4hu7ziVwCQTcaeSnBtRMYEfYR6loAA4WhlI0ooHuoop4A9wH1yGOOCBvhX+1L9povMWd roKHiLImmdC8XWGv8vpibP6hlueRptVY4UjqrOEcSLJDCmFExlCfVP0jsohS4I8akwAMSCkEkkGL mlJI4jqNRwe1RXRS2Opv4sD9UAcAMCoacA+oOkxJl9eOUeCmDqTOQXWCTXx9oeDDa6YSCAiiDOSB K28s4SaZv4HX7FA+wquHnsnto341noi0rVv3H+rbL4lLqSlSHfZmV4vbGujheyi4uTQL0kGJx1E4 e+oada6BkIZ91UywAEmN/cEuSy5Db4A96tSVrnEJndNEonh6fzmDn0bXNBBToTU18+EzjCHWegTN Ecn0HCzt2Ozs5EIaLaMmLr5fDfMAp8bry9obzRGAD/Cuqov0LMY7f1HXNL+OTYMBfR0qKbdFcxa9 QghyzRE0fAi+xSTVnNhrpKdX4zjoR45aM0nHmZ18sjfAoTx2aOm69/7hKbJv4PsNSPurTuKOQQSO sdhIRiIifiA4pHqmJoDDqmmuxB5BEhQFJdm1jUEn2URlSnvwDfv0FKyG3wbKkKNRHUZ8BW7c6cvx 04IYmj7hPUyTAyQojK5R+qlOW9Hwm843QXsUcjq4jmctVtJpxSkONRxUl5ARUG6aD7l8hBW7GqyJ fFqyKDWKHI8u+JFZfXEj6pez7TCT01Al97i/p9gUfgEcvNzlc0tK9gTsDGG9Tg0Od6XD4UOme6l+ y6/nT8pbO1lZg6QfFLI6FbUC4riA9dli4FwNuMeto+Be3hmBalGTI1JVhc0OGyBWjwe+VJ8DZNqv aShzptYg5bfkHkKPjTtBaqotSx1Ir4d80Al4kRB02KjGOkaMwm0E7wxglSsBHvEG9n6COXjBqcZi yvWodMp6E4hDowPJDwdbod7MgyoFZE8SRPDt8fqax0UVrhzgO+QeNnwBFfKhEtkadwi8E3zVzRVJ rMJWQhJHJ1wZoYIClMHzahYdG7yZpv/QLZ8kIvfWr5No7pT0pksgXPTTAsneSFCYVKmeGTiSOIL5 Z5bB/I7I7EsDWmUjeTQcIjXr9lSTp0oWYqE1c8MuYTJQtdzsNNiX1NnI1621wvJqLEENSKTJu8SS LcGcDi4d/WPzTv/Bxv4PE1rk4ZKkzRs9q5HDoyggiJd4pWmK9YGDVpuqRkz69rw9oKdxPdXp0bDc Dio9VDH0J2ihiNPqlPD3IMpJuXgwmchvnfwfLxgDMPDgZA2bib9Slwdzw+1+HKrmE8+PxVYMyNCM Flao14Ok1LC6RMip+cHT8UAG1BA0tKcJCXTqxolBbbH4J43ZXvwSjnNB2cbaudJoETWss/uJ7NOg lVYaB7ilARfR8lyid58199OA/ApCZx3hH7yIhIJuG2jVPKkGu6bpnAuOJLdAIizHuGqVyFcgUWUZ KG0y15L63BtD17wD9wOeSj1T5D+WO4u7MKxq2c0qIyGpD6mbz910Fus1drNhigAJS5DkMr0rmoyp 6tJHNawQMBRrBig7cLmyCAqSL08Jo001uhrQnTq2P2gjBNAMddXj2L2iWWcYmjtYSD6d8i/5ILbu qvmsczP02+cUYJbfh3iV4w4hb2XDt1GkpREbsSjeRzXN5hb0vNrAR6egXNpXjb/yhx+SKNMzAbjs gc5GWdV6sxwquzEIGOvmW2z2G+6nzEzV78G5qOG1Bb/BT5dKlDX1GzdQ3DrcEMkOWqE6NAQpk85r QnwTatZiwWIsWB1CgLKPerul97+XPwyLO5osepITqJbxhu1B59fiJydDQv2BfZF46vmHN3lEMCi7 TZoF2xqrk5l3kkvUjqaz0QMPMPY7OYU2xse5BoQj3nN/3kYz3IA0oPoeCIBZNGV4HEWZQEAKK6tt AICMXif6BFPFmmCCj7JMOCY4EcSEBwkqMcMI6HAKDQ7cuvqGIwFlil/j/ev1roCWqyPJRpkNUvPu FJDBqK5DmiESJJlU04D6M3NEDdNIGsvRo4MhvSgRqIOsxfXBAKqFjPcmswK+oC2oTsoRGbUWdRr7 Nl7tNDKqURHN/oemg4ItcSw7w+7WVg/SiTWjyXWaD6I9CQmEst9vIHtMLPfl1oY64rGehkQfWABS Jya+vNBwYLLUJ2Kr6ykc5CJ1U8X4QzYAjyrL6CFMh5j8UgMQpXOP9pnsQfmhLcRlL4ig90Cbxzcw gcRIQ2dU3DBWHoHJrQ0ShjxL/nX0NjwjUQaaw0w6/OEWkUnariMXIdf+OTFmX2peXse4G/iRzkZT ZhkNDDTC7xxdAV4xdPfIlBDXtiFxkjVkd6YO8KFKXDfJOlPndkdxRCgujZvBJhq0peiQ4SRDjBp6 B/vMoxnhnie4RsY2YNU07TxhbLB+4H5fQ3Mh3a2qrwahB7lzcNU06QrhGn5ds1/QmWbWhFsaLIJD Eyn1Oe3y4l4Q0rfehdAYmB4/x4TedLCCE1SvMWMrAvdUqIo3n/w9YhrkJ0mdPE58Y/wwYQk7w0Um lCbMiUBGR1XCxhqfC1fjspqoF7iOofM1zYW+riYpAernNh07SC2rpUjtJxaFzxwaKQlA2xDz4xmn JuwjDCG7rlFu9bDtII11lKWRPA0x4x9zCxnvj1VtGvxTcI+SRWp46nmYAYaGGjWJnMkehB+IWnS2 eDXFyEvcNHwUCSI6eNNX0BdKSToBQDsjVDZ1CSi5bN5LZ4tIkjeTSiTeCJbOYp0O7qvmzH3YmnRr B9O9gCbNK8JNqHUNbJKWW32uGzQrfZK0etTBvahdLT2HEA64mqIOa4Fm2PHXn0Ur9aQ5B2pnmRw8 NbRY2oar5+eZIyA2sMNwBA5S/VOCisfQYEk1TLqpl9SlqSq+Xs9afEaYe1dLS0/awCxoYfVIwn4c H48bqGhWT+CwH3IO3UxP7xKNrgbnLXouakx0EEsDWaGvqKHtC67yqoxTUl/LdTg48sOSodASWa1j T9PzolfTU+VNz1X1RPAjmMPxykDklaR9AnWie4WOznpTnkkCHsjrmqdapgyRT6igip53MJwpQGJK HbVi8JNRj02NSPSLjlcLCdGEUrhmdAmmD8rKppLBEaFNyL1Un9btcrN1k05eZzqpo3CAaWypj07W hXq4iTJU3QSIGCWATB7xgoWsBn+rHlRa9sZ/uXGdKefTp6KProREm2tDrX5gMOiYS04rA7OECAQj o5H2DchHlaACURgzStUMZB1CBKjQIbIeU7h6hCFPwJzKyWQJLrbikLbOSPQYUIKBm44WdOSMggsa 5NimIWQYGBOv6bnzFBvxJRN4I5ahUU0NZn8UzHtOMFFCX56nBnUK8ZqIvrFFevghl6Sz+uayBgm9 ZgyDBtJmbhFgTzjFjIje7+BJz0hxeUofQ7DG2yAN8EBIHuhvojz8GsIoa84BUbI3BTNlb7l6h2qp hX5OelNUk+0oQBai9hED5t6ztTAP2OwdXIwFowjiOdgjZLKGwZAx4IkGT2AE9HdYalurQQnDq13G 3ajrtNDspnG940A3oVrOmgJGP6u48TR6OlKPrqgQW09qPgQkgzqKesYQhSe1aWBBBTPPLk5HlMhj QifqTnqEURYNku9o+S10q58nw2BkWKiTYborwgcPUdD5SoKYw5NefviNE14g5x2fTnkl1OKqUJFp X2oDpzF46JtJfMkDY2fYBCBdz36YE1o8FA7v+F1HZD0t9AJ5pIfH1AtXc2kfH18RoUMRXHKaGqtC koCIMLRTu1uSViM2vK9107F60fMiFDsCWkdJaAlNaBM5k7pVOpEczXQERb4bEOUmyQP1g+Bq8kI9 a6zPSO2typ/2zqMBOunxVN+zbijnrcn/s9/B+9HNu4wukw7T83vgY4leR216hq8gJ/4atfvDR50V oYfjJs+Hns6gJBFVgRtDcz/dVzWKJjMN0kSp7aOZVJUoOTQ0olmVZ+oPqI2Ppspub13/bJ1Bn6Sn Q9mwh4Cwj5FLSU92osHUX9bp5oJsxmtzvNEANSo8RezQ/Yb8kzKlrN4jbgSraGQePxDmuSVojOLE CrbqcqyF+kQ33M8MjnyrBnSfziftK/be9tLAINTDRmkGi9gWSoF0uJRmQPdpDk/PLV0ADluCZoSP uh4XUjcHhaZDfZ1WaVz1Z+fmn1Pn7yNQsd+BySZ2NSZN8qCr9RwkpDaohYCPnntNzZW8J3LxyQgZ nCh4HlBw2PIuiFGXfgIiOJORn1oF4aYzyduqVr6OVC5bY2oDZj2VxtvnL2gYGjW195CwDuajxa1D VjXHMf5Re3SKznG9lGR9z38APZJtRfP3iQpCoySVLIgVSfqkwRrWpPUkEUM4OpB0U8/7SI0B+Tr8 4EavngjWk2WX6vTvXXwFaTs0RxKfHpr6BBhp4kZ234M/dqaDPfYew6mMbmW9BlLBpxw525OazIUK mUAMwGXcSr1kdNMkijrywPdTIurPhzdiWvRchqwxmoq1AL3kzqc1R8mLvNOA5VbS4814OjiU6g2I K5LTsbJeyUc9uAQdWCJ3tek64JOE16HKgdflpIHzg5Ib0WNY3yOKVNc7uV9HTx5wIx0Q1bM9Wc/H k5Gkh+6Dv/RIlGev0aZcB1UZBYKjUrHheXBqslFzbqVeNKSnZ7bRAEfPRMfyebhR7HGEcn0GMepr DOwD53T1RJYH/zQE08pG1gRqGjGlaRkh4ClH46Vk2ztGPG/qFYgU6CiEpoPoEZrO+WY39T0k7TJQ 2+9eSP6qZ0HgKBkWPfFC4QAz8GbCwqetVtnUDH3S3lpDTLaugXwg62AuXfIkGZRwdQKIFNPTtX6p DCEaOXboGQH2UsdrSmUHjRNBsFEzEtKIE8lwBCN+Kcbz6FgMX1LzdxCFxNQzQzrl0nSIpog0GaPx ZsLDW6mVYeqGkIWI0bcXFDKyeL/HBgBAzXNnjLaUERiNBY3tDY2N5ypAxL5WUJ9OD4xoUMoVZIPX oauMWEuaurohv1lNxEjT+SI5taY/kFbMGvzCzC/SCIaeI7//9SH3oedFEE0JA6MHcqm1jI9fOB+r OPG4EYhqIuyiJxFgSz1UEwkBQG16cqXBj3oYwelOUFpZruw9qvhhh0qNjyKhG350qFeu/7G5mN8D 3n99Iet/Q2gvHBSbZE6WTCokE6BG2mO0IGvizW6RVOx505PNcDxRa3r6CQEXvB6m7UJPQttSCipM gYKGmqiLADCR8abHWNWt8hnZoqN6PVzg9B8fhKa+Yqd6UBEgiOYDNGJPhlQVp5ralw3FZr80Bx7f I0ki+aP0R5kja6ZXS0r9dY3fLD0VyIrwj5qD1YNRUf85gE2JYB81YkcilKonJjUtU7UIrCiwOXfS WXLVVKkGZ5se2YcxUZRbUK4HTXW8KV7X1O3GcRfelUxImtaMmlVzSllji4KmdTp3MtS01DPRGmwU MEUJJZXYAdbxXWp7ck8oGw0WayrjprXca1/gSlFQjcgPNfXe0A/vULFvvq0IFAhh35OkS892Q6A6 Dz49awoBbmnBAVO6p4LZ0DCvBpkqea4BMMqcOtGRVVZ7RA+qDT0llvFLbKz+xwmKHGJPXNcl6RxI nIxPwEltOneJ1aLO+kMEVr8tCP8f07Hdu4f7X5ELM70dRvn/AAABhGlDQ1BJQ0MgcHJvZmlsZQAA eJx9kT1Iw0AcxV9TtUUqDnYQcchQRcGCqIijVqEIFUqt0KqDyaVf0KQhSXFxFFwLDn4sVh1cnHV1 cBUEwQ8QRycnRRcp8X9JoUWMB8f9eHfvcfcOEOplppod44CqWUYqHhMz2VUx8IogugCMYkRipj6X TCbgOb7u4ePrXZRneZ/7c/QoOZMBPpF4lumGRbxBPL1p6Zz3icOsKCnE58RjBl2Q+JHrsstvnAsO CzwzbKRT88RhYrHQxnIbs6KhEk8RRxRVo3wh47LCeYuzWq6y5j35C0M5bWWZ6zQHEccilpCECBlV lFCGhSitGikmUrQf8/APOP4kuWRylcDIsYAKVEiOH/wPfndr5icn3KRQDOh8se2PISCwCzRqtv19 bNuNE8D/DFxpLX+lDsx8kl5raZEjoHcbuLhuafIecLkD9D/pkiE5kp+mkM8D72f0TVmg7xboXnN7 a+7j9AFIU1eJG+DgEBguUPa6x7uD7b39e6bZ3w9cRnKeodbOXAAADRppVFh0WE1MOmNvbS5hZG9i ZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3pr YzlkIj8+Cjx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IlhNUCBD b3JlIDQuNC4wLUV4aXYyIj4KIDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcv MTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+CiAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9 IiIKICAgIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIgogICAg eG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2 ZW50IyIKICAgIHhtbG5zOkdJTVA9Imh0dHA6Ly93d3cuZ2ltcC5vcmcveG1wLyIKICAgIHhtbG5z OmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIKICAgIHhtbG5zOnRpZmY9Imh0 dHA6Ly9ucy5hZG9iZS5jb20vdGlmZi8xLjAvIgogICAgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRv YmUuY29tL3hhcC8xLjAvIgogICB4bXBNTTpEb2N1bWVudElEPSJnaW1wOmRvY2lkOmdpbXA6OWE2 NmQ3M2QtZjRhMi00ODM2LTg1ODAtZDg5MTlkZjJiNTVmIgogICB4bXBNTTpJbnN0YW5jZUlEPSJ4 bXAuaWlkOmQ1Mzg3MTc0LWM0YzctNDM4Yi1hYjkwLTgxYTZiY2Y2OTUwNSIKICAgeG1wTU06T3Jp Z2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOmVmNmNiNjdkLTZkOTktNGE0Ni1iOTUxLTU0MjkwMzNj ZmYxZSIKICAgR0lNUDpBUEk9IjIuMCIKICAgR0lNUDpQbGF0Zm9ybT0iTGludXgiCiAgIEdJTVA6 VGltZVN0YW1wPSIxNjU5NzQ2NTI0ODU3NTIzIgogICBHSU1QOlZlcnNpb249IjIuMTAuMzAiCiAg IGRjOkZvcm1hdD0iaW1hZ2UvcG5nIgogICB0aWZmOk9yaWVudGF0aW9uPSIxIgogICB4bXA6Q3Jl YXRvclRvb2w9IkdJTVAgMi4xMCI+CiAgIDx4bXBNTTpIaXN0b3J5PgogICAgPHJkZjpTZXE+CiAg ICAgPHJkZjpsaQogICAgICBzdEV2dDphY3Rpb249InNhdmVkIgogICAgICBzdEV2dDpjaGFuZ2Vk PSIvIgogICAgICBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjE2NmRhYWY4LTEzMDAtNDgyOC1i MjYyLTAwN2FjZTg5YTMyZCIKICAgICAgc3RFdnQ6c29mdHdhcmVBZ2VudD0iR2ltcCAyLjEwIChM aW51eCkiCiAgICAgIHN0RXZ0OndoZW49IjIwMjItMDgtMDVUMTc6NDI6MDQtMDc6MDAiLz4KICAg IDwvcmRmOlNlcT4KICAgPC94bXBNTTpIaXN0b3J5PgogIDwvcmRmOkRlc2NyaXB0aW9uPgogPC9y ZGY6UkRGPgo8L3g6eG1wbWV0YT4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAK ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg IAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAg ICAgICAgICAgICAgICAgIAo8P3hwYWNrZXQgZW5kPSJ3Ij8+vQWh3wAAAAZiS0dEAAAAAAAA+UO7 fwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAAd0SU1FB+YIBgAqBHHUThMAACAASURBVHja7V13eFTF Fz3zdtN7J430QkJJ6B2kVxGk2RACIqIoKtgL9oIKiIpSEhAR6UV6k44ESOghnYT0tpu6u9ndN78/ KD8h5b3dbM+e7+ND2VfmzZx33507954hMEMjiA6c3qYeJIAhTFuWYb3BEneGUE8K4gXAA4A7KCwA OIGAAWAJwO7e6bUA6gHIQVEDBjJQlIKgGKDFoCgFJYWEoZkMy2SmZPvfARax5l5vGYi5C1QgePQk y/pauygwgigCtj0lJJpQRAJoC8Bah02RgSALlKZQwiQTqkyyYJF04/baIvMomcmvEQQHz24rJMre lLI9CUEPUNL5nsU2VOSDktOU0ONQMifSc1anmEfRTH6+ZHcSEsUgSslQAjoUQKiRP1IRKA5Rgt2W NtYHb9z4pcY8ymbyP0B4+Gx3qlBMJMBkAP0ACE30UaUA/iEU2+REuDUra2WlmfytEIGB050tGDIe hEwhFINNmPBNvwgEfxMWf9i7CvdfurRSbia/iT9rZOCMYUoBmUsohgOwMn/4AQCFlGI1FTArMzJW 55nJb0KIjp5rL6+TPg+CeQAizFxvEgqA7mYZ8l1GRvw5M/mN2pePC4YCswG8AMDVzG2VcIal5JuM 7DV7AFAz+Y0EwcGz2wqp4n0QzAQg0NV9GYbA3cMJvj5uaOPjCjc3Bzg52cPZxQ4uzvZwcraDs/Pd /yek8W6XyeQQi2ogEtWgoqIaFeXVEItrUV5eheysItzOLkZNjUSX3ZnIUvJ5Rvaav83kN2BE+Mf5 sBbkAwI6C4CFNkkeEuqDiAg/hIX7IjTMB2HhvvDxcYWFhfbnzcXFImRnFSE7qwipqflITspAelo+ lEqtLvgeI8DbqVnxF83kNySf3m+mq9ySvkWAeRSw1fT1bW2t0L1HBGJiQxATG4IOHQNhb29jUH1Q WyvF5eQsJCdlIOlSOi5eSIdMpvEADgXBHyyhb2ZkJJSaya9XLGLCg+/MAegXAJw11iGEICq6Lfr2 i0bffu0R2zlEJxZdk6irk+H0qRv45+hlHP/nKioqqjV5+TJQ+m5adkC8secXGSX5I4Ond6CU+Y0S 9NLUNdt3CMTI0d0wcmRX+Pq5m8ynXalkcTk5E7t3/Yt9exJRVVWnqUtfoix9Of12wnkz+XUAP79J NraWDm8DeBcayLEJCPDExMn9MGp0d/j5mw7hm4JMJseRw8nYvu0Mzp25qYl5ghLAdxY21R/duLGl 3kx+LSE8OG4IKH4DQXBLJ6w9e7XDlKkDMHR4ZwgEDFoj8vPKsPHP49i08YQmvgbnlYQ8m5m5JsNM fg1i4MBFwoLc3A8AfAhAbaY6ONhg6tMD8dTTA03KrWkpqqrqsPmvE1i/7iiKikQtuZSEgLybmrVm mZn8mvDtA6cHsgKyEZT0VPca9vY2mDZ9CGbEDYOjk62Z7U1ALldg6+ZTWPHzXhQXi1rCqPUW1tWz jMENMljyh4fEjQfFGgAuZtLrdl6wYf0xrPh5T0vcoWMsIxufkbGhykx+FRAaOs+KsLVLCTBHnfMF AgZPP/MY5r02Dk7OdmY2q4mKimos/X4Htm45pdbEmABJ9ZQZlZ29uthMfh6I9pvpWm/BbiOEDFTn /C5dw/DRomcQ2c5f4227lXIHXm1c4OJi36pegpSbufjy87+QeD5VndOzCcXI1Oz4VDP5m3Nz7iai 7QUQqeq57h5OWPj2RIx7oleTeTPqoKCgAtu2nEJpSSVemDMS/v4erfZLsG9vIj7/ZCPKy1X2ZCoY Qsfeykw4ayZ/Y8QPjusLYAcAlcMwT07si3c/mAoHB82kHFBKcfZMCv784xj+PXcLbyyYgKeffUyj L5WxQiSqwacfb8C+vYmqnloDgslpmfH7zeT/DyKCZk6ihK4DoBJ7nZ3t8ekX0zB8RBeNtEOpZLFn 93ms/HUfMjIK0K17OL76Nq5VW/umcOhgEj75aD3KylT6CshByOS0zDU7zeQHEBYy41VCyVJV29Gn bxS++jYOXl4uLW6DQqHEls2nsOq3/cjPK4NQKMAbC57EjJnDwDBma98UxOIavPt2Ao4duazKafWE oZNTMxJ2tWryhwfPeB0g36vSBktLIRa8NRHTpg9psRtCKcWBfRex5PvtyMkpAQD4+bvjh6UvolNM sJndPPtwXcJhfPftNsjlCqP7AhD9ED/uDQDfq3KOm5sjflrxMjp3abmayJXLWfjskz9x7Wr2g38b NCQG3yyeCUdH85qAqrh6JRvzX/0V+XllRvUC6Jz8YSFxCwnFt6qcE9nOHyt+mwcfX7cWT9i+X7wN 27acAsverc4jhGDuK2PxyquPm92cFqCqsg6vzVuBs2du8neBKCalZsfv1lebBTolftDMBQRYrMo5 Q4bG4rfVr8HVzaFF997z93nMjluGpEvpoPeqUu3srLHkxzl46pmB5mhOC2FlbYGxj/dEZWUtrl7J 5sc9giddnbucrxAnZZm05Q8PinsBBCtVOefFl0Zh/hsTWmSRy8ursOjD9Th0MOmhf/fycsHKNa9p ZUGsteOvjSfw6cd/8F0ZrmIZ+lhGRkKSSZI/PHj6KIDZBZ7iUIQQvPfBVEybPqRF9z3/7y28+fpK lJY8LE4WFuaDlfGvw8fHLOqgLRw7egXz563gW05ZKgDbJyVrbbpJkT8yZFYXlrLHAfDKC2AYgk8+ m4bJU/urfU+lksXyZbvw24q9D3z7++jVux2W//KyxhbFzGgaZ07fxMsv/QRJnYwPFTPllPTRZS6Q VskfETAriArYswDa8HICBQw+/2o6JjzZR+171tRIsOD1Vfjn2JUGvw0Y2BHLf5kLKysLMzN1hGtX szFrxlKIxTw0cgn9lyX2AzMylst00TatTXiDg2c7EUZ5mIBf5ZVAwGDxDy9g3BPql+VmZhRi2jOL cTk5s8Fvw4Z3xo8/vwRLSzPxdQkvLxf07ReNQweTIJFwpfgTPwJ5QLkoeYfRkn/gwEXCGnHFPkJI d77EX/bTSxgxsqva97ycnIXp075DcSPVSKNGd8cPy16EUCgws1EP8PBwQq/e7bB3TyLq6zkXwzq5 OneurRAnnzVK8lsJgr4ByNN8j//si2kYO07tYi2cOX0Ts2ctRU11QzWz4SO74oels1ttra6hwNPT GdHRbbFv74UG87CGAQ8McXWJSaoQXU4zKp8/IijucUqwk++1574yFq+9/oTa9zu4/yLefH1Vo8vr vXq3w8o182FpKTSz7x5kMjmKikQoKqxAYWEFCvLLUVJSieqqOtTUSFBdfffPfyepEokM9ff6187W Gja2VrCxsYSjoy1sbCxha2sNN3dHeHo5w8Pd6e7fnk7w9HRusGK+d08iFr6xik8YtJplaM+MjISb RkH+0NDnQxhWcAmAE5/jJzzZB19+M0PtBabNf53Eoo/WN9qR7TsE4vcNC2FnZ90qSc6yFBnp+Ui9 lYe0tHxkZBQgPTUf+fllnJZXk3B2tkdQcBsEB7dBYJAXAoPa4EJiKn5fe4TP6akKCHtoayMNjZF/ 4MBFwoI7Oaf4Fpv37d8ev616VW0/fNvW03j/nbWgtOFABgW3wZ+b3oGrq0OrIbtMJseli+lIupSB 5ORMXEnORHW1xPgfjNB/CUtWg2WOpeaszjZI8ocHx30MYBGfY6Oi2uKPv95W2yofO3IZr8z9uVGL 7+hoi607PkBAoJfJEz7ndjFOnLiGUyeu40JiKo9oitEjG4QcJcA2b3//I8ePL1LonfxhgTN6EIac Bo8VXCdnO+zY9ZHa2jn3ozqNLZwwDMGKla9i4GMdTXb0C/LLceRIMg7su4hLF9PRilECYAtl6Xp1 JRNbTP6OXs/ZSewskgkQxnUswxCsTngDffpGqXWvtNQ8PDP1myYlNV6dPw4vz3vc5Ea5pkaCPX8n Ysumk7h+7bZ51t4Qlwgl36Rmr9kKFTbTaHEYRGpr8QMf4gPAvNfGqU38stJKzJqxtEniDxoSg5de HmtSI3or5Q7W/34U+/Ykoq5OZqZ40+hCCd0cHhyXSFn6Kt8vQYss/72wJq+StJ692iHh9zfVytBU Klk8/+xiXEhsPOzr4emEvfs/MxmdnnNnU7B65X6cPnXDTGvVoSSEfO3t77+Ia06gNvnvbQqRAsCT 61hHR1vs3vcJvL3Vy6L8+stNSFhzqMnfV6x8FYMGdzL6UTt98jqWLd3JNx/ejOZx1EphNela7gqR xt0euSU+4EN8APj402fVJv6BfRexNv5wk7+Pn9Db6Il/6WI6vl+8zWAnsJaWQtjYWsHRwRYWlkLY 2FjCxsYKlpZCODjYgDAEAoZ5sGONlZUF5AolWCULhVKJ2lopAEAsqoVYXAOxqAZicS2kUq1GpwZL hbKTISHPDc7MXF+iMct/L1szBTz2sh07rie+++EFtVp/O7sY4x//pEl/18vLBXsOfGq0dbeFhRX4 9qst2L/vQqPrFdqGlZUFfP3c4ePr9mAjPS9PZ7i4OsDF1R6urg5wd3fU2kJhTY0Ed3JLcedOGfLu lCI7uwg3b+QiI71Aky/GNQWE/RpbKFPL8lMB+wUf4vv4uOKjRc+o57gpWbyzcE2zE72PPnnGKImv VLJIiD+En5bt0npsXiBg4OfnjpBQbwSH+CA4pA1CQnzg5+8Od3dHvfaDvb0N2kW1Rbuotg36JzOj AInn03DhQirOnrmJqkq1RXM7WECxGZg0CtiibJHlDw+e3g1gznOdyzAE6/5YiO491Nv3efWqA1j8 9ZYmf+/aLRwb/nrb6IifnVWEd9+KR3IjadcthZ2dNSLb+SMqqi3aRd8lVWioj9HnNsnlCpw9cxMH 9l3EkcPJ6qlHUyxIy47/voXkjzsKYBDXcc9OG4QPP1bP6mdkFGDC4582WQJHCMHGLe8iNjbEaAaQ Ze9q3Cz9YYdGPukCAYPQMB/Edg5FbOcQxMSEoG2Ap8krUMjlCuzfewErf92H9PQCVU6VUSUTm56z OkUt8ocFzxxNQPdwHefgYIPD/3ytlqKxUsli6qQvm414DB3WGT+teNloBqygoAJvzv8NSZcyWkT2 Dh0D0bt3FLr1iECnmOBWm7QH3BXM2rP7PBZ99IcqG3OfTsuK7497C2EqfQ8J6Ht8jps9Z5TaUt6b /zrRLPEFAgZvLJhgNIN05vRNvDn/N4hENSqfGxLqjd69o9CrTzt07xFprjt+5Os/dlxPdOgYhLlz liMzo5DPaX0jQmYOT81cc0Al8keGzhrIsmxvruO8vV3VVl0Qi2uw9IfmRbxGj+2B4BBvo7BMK3/d h2VLdvLe3MHD0wm9+0Td/dM7Cp5ezmaWcyAwyAvr1i/EM1O/fiA7yTEuCwCoRn5Wyb7Nx0l67Y3x sLZWb5fQZUt2chY6z5g5zOAHRKFQ4oN312LHdu5KvOAQbwwZGoshQ2PRsVOQWTxLDXh4OmH12tfx xJhPHqwpNIPBoaEzOmdkJCTxIn94UFwnEAzn/Dq081e7AP1Wyh1s2nii2WO694hA1CNhMUNDba0U 8+b+jDOnGy9AYhiCDh2DMHRYZwwZGoug4DZm9moAbdt64p33puDD99dxHssoyXQA/MgPgrf5TI7n vDRa7WjDku+3c7oHT07qa9ADIKmT4YW4pQ1WagkhiIkNwePjemLosM7w8HQys1ULmDSlHzZu+Ac3 b+Zy8XkYr2hPu6CZAUpCM7hcpLZtPXHgyBdqFYpfv3YbE8d/3uwqp42tFc6eXwJbWyuD7Pj6egVe nvMTTp649pBLM3pMd4x9vEerKK4xBOzfewHzX/2V2/qzbBCn5VcS9kWAcB4368URaiskLPlhB+fy /uDBMUZBfEcnWzzxRG+MG98L7TsEmtmoYwwb0QWOTracK8KUMIObJXV09CRLuYTM5DPhGD9BPZW1 5KQMnD55nfO4xww0eY1lKRa+uQrV1RJ8891MjBjZVe0Jvxkth0DAoF+/9ti7J5GD/DSkWfIr6uwn g3Bnbk6fMVTtJfQ1qw7yeqD+/TsYZGffzi7Cy6+MRXiEn5l5BoKY2BBO8gOMPwdjyVyuG9nYWGLy lAFqNTLvThmOHeXe0ykqOsBgd1E3hjWH1gY/HvXhhNCmyR8ZPL0DC3DGLceO66k2MRPiD/JaADKm HB4zgOpqCaSSekhl9VDIlairk4FS+iAhraZGClbJok4ig1yuQH29AlJJPZRK9kGqQnVVHSi9GzpW KJWQSeVQKJSws7+b0uHoaIvQUB/06ReN0FCfh+7v4sqdXUApXJskPwtmOp8HffqZx9TqoLo6GXZs 4yfH2CnWvEGcNiGpk6G6WoK6Ohlqa6Worq5DXV096uqk9/7/roJbXZ0MtTXSe8dKHxxfUy1BbZ0M knv/r2v07d8en33x/IP9FqRSPnsCEGWj5B84cJGwIDeXU2sztnNog1xsvti/9wLvjnr0zTajcSiV LEQV1ai496e0tAqV4lpUVdVCLK596L+rKusgrqxFVWUtH/FYg8bpk9fx5LhP8dfW9xAQ4Imqyloe Z9GqRslfcCd3KHho6j/19EC1G7xl8ylexzEMQWBQ646Ri0Q1KC0Ro6hIhNLSShQVVkBUUYPy8mqU lVWiorwaIlENKiqq9VIRZgioqKjGqy//gu27PsL16znc1AdKmnJ7xnOdbG9vg2Fq7n6em1uC5CR+ 6b0eHs4mHTosK6tCQX753T8F5SgsrEBpaSWKi0QoKRGjpFhs9JZZV7iVcgfLluzgFTonBGmNkH8R A5o7huvkkaO7wcZGPVIeOnCJ97Et3YXRECZ/WZmFyMsrQ0F++YO/8/PLkJ9Xru0i7laH31bs43kk vdqA/KGBed0BcMbvxoztrnYDH90ZsTmoWxegL+TmluDMqRs4dzYF167dRkF+uZmRKsLWkoVQeNd9 s7dmcT9vQKYgKK3SSEkmFSgsTja4kkBAx3C5jY5OtujaLVytuxYViVTSpTGGaqWaGgl2bD+L3TvP tWrNHQFDseftbDAqCG/bWrEQEkAgoLCz4g57l9cIsGSPB3Yktig58FpKzsrCBuSnlHImzA8Y2FFt afEjh5JVmpQZcnp7pbgWq1cdwMYN/5iGHHgLoWQJBAzg6yrX2j3c7JX4dEoxbuVbISVfXcNIdgKP ZGoGBk53BtCZ69QhQ2LVbvy5szfvkZrAwfFuWZ5SweolPqz2ICtZbFh/DD8t341KcS3M+D9yyizh 6ybX6j0YQjGpVyU+3aoe+VmG3dWA/BYMMxgc+3QJBAz69ItWu+Fz543Fu+9PhVcbZ1hY/P/2Mpkc GekFuHbtNg7su4Dz/94Cy1KeCxa69enfemO1VqRHTAF3ynWz22WvMLWNztWMjITkBuQHyGAuhecO HQNbVEgdHR3Q6L9bWVkgun0AotsHYOpTA5CZUYgvP9+oVuG3tvDPsSt4c/5Ko/pKmSr523rI4eMi R4FIpfspKGUW4B7JH0nAp/24zu7dO0onDxcS6o3VCa/jifG9DWJQf193BC/P+clMfC63p1R3azJd Q1SaZ5VQYGp69uoHwq/Mf/19AnAyu1efKJ09HCEEz04bpPcBXRt/CF98upG3CkNrRm6Z7jb57tiW F/krCcU4YoHQ9Kz4bf/94YHbYylgeoGi2VIsoVCATjGtK8lsx/az+OqLTWZW84SG4vC8EOnLa8MO +2qZ8EhB9soGpV0PyE5YyulfRET4wcrKotUMZNKlDHzEQw3AjP+jSiJAvUI38ekIXxkYwhk2Fzja sO0a++EB+VmGdOO6SodOQa1mEKurJXhz/m/mvBoVQSlQVq0b629rycLPnXt8WND2jXoyDyw/RQwn +dUsyC4rq8K/51JwJTkLOTklEItr4OrqAG8fV/TqHYU+faMMbiX3q8//QkFBhZnNaqC8WggfF92E qNv5SpFb2rw3QsA2Tf52AbO9lVBw5g136Ki65S8uFmHr5lNIPJ+Gy8mZDRK5/vzjH9jYWmHqUwMw 64URcPfQv6bN1SvZ2L7tjJnFaqKsSqCze0V4y3DwsgOH5ScdmnR7WKGSUxrBxtYKoWGqF5V4ebng 5XmPY90fC/DvhaVY8uOL6PiI+ySpkyFhzSEMH/I+tvLM89cmvvlqs87y4jsHmV5aRHm1YU16CUVk k+SnLOVcso2ODlBbl+e/L9Co0d2xZfsH+H7p7Aa1vzU1Erz/7losfHM15HL9+NrJyZm4eCFNZ/eL G1SBEC/TSmuukjI6u1egB6++8x04cJGw8QkvAWf8Mrp9gEYbPWZsD+zY9THatm2ojLJ75znMnrms yc0ptInfE47o7F4udkr0iajFmC5VJkX+OpnuyN/GWc4n+VFYnHPHt3HLT8ApjxCkhVJCP393/Lnp HXh5uTT47eyZm3hz/kqdLizV1Ehw9Eiyzu43vnslLIUUE7qLIWRMp/xQUq878ltZULjZc3sJcsoG Nkp+QrnJ7+/voZXGe3g6YenyOY2mSB8+lITly3bprCOPHbmss6+NkKGY2kcMAHB3VGJU52rTsfz1 us1D9+YRWWIETGPknyQAwCnB4B/gqbXGd+4SijcWPNnob7+t2IvE86k66cSTPGo/NWf1qx7Ke39p WDkEJmL9pTq0/ADg48pjfkjZBn47Exlo5w+g2WwkgYB5oImiLUyPG9qo+hnLUrz/zlqdWORLOpro 2liymD304fLGtu71eLybafj+Eh1bfj5rChRMAzUShmUEnC6Pt4/rQ7n32oBAwOCVVx9v9Lfc3BL8 vla7E9GysiqdLWrNHV7e6IAtGFsKN3ul0ZNfJtex5XfmtvyEULeGbg+lnJEebfn7j2LkqG5NCr6u Xnmg2Q2pW4qc28U6ecZIXxmeHyBq9DdnWyUWPl5i9OTX9W6oznwMBkUj5Cfw5SR/W92Qn2EIJk/t 3+hvYnENtmw6qbV75+aWav35nGyV+GFaQbO+/diuVZjcW2zU5Cc6nrs42PAifwO/naEAZz6Bp6fu dgUcPaZ7k8XxO7ef1dp9K8XarRgTMhRLni9AAI9FmfcnlKBnmPHWBjM6vp+DNXc4vFG3hwCczNbl /q+urg7o1bvRDFTcvJmLjIwCrdxXmy6VkKH46pki9AirU+FFKUT7tsZZNcYYoOWnIK6N+PzEhfPi jrrVxu/Tt+lsi907z2nlntpKXXaxU2LFC/kYFataJMfRVomEuXcwuH2N0ZFf12oz9ta8FkLtGlp+ hnJafkcdk79rt7Amfzt8KNloSDC4fQ22LbiN3hHquTC2liyWzcjHx5OK4WxrPFEgRsd+j4MNL/KT Ll1mP5T7LKQULoZG/qjoANjaWjXqimRnFaG6WqJTV0wlq0eAoR2r8Vx/kUYyNgkBJvcSY0Snamw6 64SNZ1xQXCk0aPLbWeu21tnGgoWAAbgyYaRSoRUA+X98fm7L7+Rkp9OHEQgYhDShyU8pxY3rtw12 4Cm9W8dqJdSs32shpKhXElRJGBg6nGyUOjc4PMoZoVTWPLSdJ0NBHA1pwnsfgYFNp1Ncu3rboAc/ OdsGU5YGYNHmNpDIiEauN+H7QPxy0F2nSWNa9sF17mqxLB4qFxSCY3NpALC01H3RetuAprNIb6Xc MXgCUAps+dcJl7Kt8eP0AgSpkbOvZIHlB9yx5qgrWEpgLHC00f38hE/vEJnlQ2k8DL+B1H3Clbd3 01ORigrjyYDMKrbC1GUBOHpdNan1yjoBXlrth1VH3IyK+Pqy/IQHk6kVS1QmP9GDVLK9fdOulrGJ w9ZIGbyW4Iu/zvBbLCyvEeD5n/1x5pYdjBEOeohMMeDj81P5o+TnPEsflt++mXmGSGx8sW9Kgc+3 e+HPU80H18qqhYj7xR/phVYwVrRx0n0JKh/7bN0I+bkHDnogv33TUiZVlXVGSQpKgS93euJYEy6Q TE7w4ko/ZBQZL/EBwMtZD+TnY/mtWdUtvz4glzf96TRm1ThKgfc3eiO/ETXjr3d54la+cRPfxpKF i53u3R4+UbD6emuFyuRnWd2/H83l2qi747uhoErCYOEG74cmsoevOmDzWWcYO/Rh9eVKAgXL7ffY 10oakJ9zGVIfstx1dU3f08nIyQ8AV27bYPdFxweD991uD5gCdKXU9pDV56kW4douRPYw+QlEnJZK Dz52VZWkGfJrfodGfUS0Vh11BaXA3iRH5FWYhgCwPix/Hb/Ksdrjxxc9bPkJCzE3EXVP/rw7TReX eGuhnrilglzq4HaJJa7lWmPXBUetXF8fBfEhnroX4JJIeRgugspH/4mhhPCw/LqPq99phvztNSyg BQCMQD9pAydT7HH5tubTR5xtlZjUq1LnzxPqLdM9+flZ/kbID8pN/ird60neyWma/FFaIL+FUKAX 8h+7bq8VPfupfcSIG1gBXb/T+pBerOWRP9WYh8MQwof8urX8Umk90tLyGyephRDh4b4avydh9JNC oI09rFzslHi2vwi+bnKM66Y7629vzcLbWfcT3goewri0UcvPcrs9paW6/Xxeu3q7SaHamNhgrcio 6MvyS+WafekYQvHZ1KIHsfaFj5eolVSnlsvTRqaXTcN5qUIzaFBKxzCEFnKdl59XrtOHuXQxvcnf Ro/prpV76svn1yRsLVksfq4Qj0X/P/3D0YbF6jl5OqkHDmujH7VpUR2fsaMljU14b3OdVpBfpls/ +OjlxqMXAgbDRnTRUmTEuMnvaMPij1dzMSKmYcZrGyc5Nryai36R2nVfY/S01wCfbZAoi6KG5Fcw nOTPy9ed5b9zpxRXr2Q3+lvffu3h5qalsKDQuMlfJWHw/E/+je5SUl4jwNzVvjil5SzR2ED9kJ+P z8+ANCS/RFl5GxwpDpI6mc5y6Pf+ndhkFmlTglaagFAggLGjWirAm+t9Hkqdzi2zxJQfArWeHu1i p0Rbd/24PeU13GNHCdvAvWfy8rZIAHBq5OXnad/1kUrrseGPY437k+G+GDwkRmv3NnbL/2CQKfDF dk+cT7eFTE4wL94HhWLtF7zHBEr0Mtm9O+HlJj/LCBqS7FtWbAAAGYJJREFU/97fnK5PVmaR1h9i 08YTKClufMF5wVsTtZqCYOw+/0MDTQkW7/bAjkQnnaVHx+rJ36cUKK7kTg2xkAuKGiU/Ac3kOjk1 Vbt1sxJJPVb+tr/R3/r1b4+Bj3XU6v2FQgFMCSn51tieqLudLfW1sV5plRAy7nCx0ivIp7QJy89c 5Tpb20Xjy5bsQFkj6wmOTrb47Mvntd6Julal0wVu5ulmb2NHWyU6BuhHWrFAxCshsODRpLb/k5/g Cqflv5WntQc4dfI61iUcbjhDZwi+WTwL3t6uWu9EFxd7kyO/rqpP+0bU6m1XGZ7ZsI2GDxkAYBQC TvKXlVU1aplbihvXc/DGa781WjDz7vtTMWhwJ510orOzHcxQk/zt9CcoUMCH/JQ2Tf6UnJWF4BHx SdGw6/PvuRRMf+67BinThBC8ufBJTJs+RGed6GyCll8XYAhFn4g6wyY/IU2T/x44/f7kpEyNNLi2 Vorvvt2KuOd/aEB8GxtLfLN4JmbPGaXTTrSzs4atrZWZzSoiyl8GdweF3u6fL+IRxiWNuz0PziQU yZSgWVPb0p3J01Lz8Pfu89iy6SREoobyI527hOLLr2cgKLiNXjoyItIfyUkZZkargEHR+pWRuV3C IyuWZZsnPwT0DFiysLlrXL2SBYVCqVJYsLZWig3rj+HPP/5BYWFFo5Pa3n2i8Mxzg3Xm3zeFdlFm 8quKkbH6U8+T1DMo4rGAxwqEzZOfMhZnwCoompE9lEjqceN6DjrFBPNuoJ2dNWbPGYUJE/vi6pVs 5OeXobysCo6OtggM9EJM5xC4ujoYxic8qq2ZzSqgU6BEbykNAJBTasFHylHu5+dXlJHRDPnT0laW hYfE3QJFu+audOliukrkvw93d0e9W3Zuy28mvyoYFatfzdSsEh5zNIKMxmL8j054ARanuK515vQN kx3MdlFtTTLerw0whGJYR/2Sn4+/TyhuNvkMjxx6mutiiedT9aLjowsIBAwGDOxoZjYP9AiTwNNJ YfDkB6Up/MhvIT8GjvTm+noF/j2XYrKDOnhorJnZPDChR6Xe25BezE1+ljD8yJ+W9ns+eMT7Tx6/ ZrKD2q9/e9jYWJrZ3dz8zVGJoXp2eSQygswi7nGiDHha/ruH7+O64D/HrupFv1MXsLGxxMBBncwM bwYTe4hhIdDv+F/Ps4aSW5+TrasTpKpAfsJJ/uJiES4nZ5rs4D4/Y6iZ4U3NixjgyZ76d3lu5fPI WKW4XVCwso43+dOyqs8BqOC67r49iSY7wLGxIYiJDTYzvREMiKrRixjto0gt4BXmbNaFb8Tyb1Hy sf7791+EUsma7CBPnzHMzPRGMLWP2CDakcZr5xqiKvkBlmIz12XLSiub1dcxdgwb0QWBQV5mtv8H UX4y9InQ/35olPJb4KIgV1Qmv5Vt1UE+rs/2radNdqAFAgbvvj/VzPj/4IXB5QbRjqJKIa/9jSkj V538N25sqQewm9P12XdRL/LlusLAxzqaF73uIcirHkM6GsZGgLwWt4CajIygbJXJDwCEkE1cV5dK 67F/3wWTHvT3P3oKlpbCVk/+WYMqwBDDCG9fy+WWdCcU14BFrFrkT82sOgwgn+smm/48YdKDHhDg qfPCGkODt7MCoztXGUx7rvDYz4Al5DLXMc2I1WxRgmAd1wVu3MjB+X9vmfTgvzzvcXTvEdFqyf/S sDK9L2rdh4IlSMrmJj9D2bMtID9AFMxq8NitMX71QZMefIYh+Pb7WXBqhUXuQV71eKK74Vj98+m2 qJJwC4xRC+U/LSJ/as7qbADHuS5y4vg1ZGYUmjQJvL1d8dnn01od+d8cU6o3WZLGcOgqr5Tz1Ht5 auqT/675x3LOt4xSJMQfMnkiDB/ZFfNeG9dqiN8lWPKQ1r++UStjsD+JW6Wbghzj9UXnOiAts+0u AJxO/a4dZ7Wi62NoeOXVx/HUM4+Z/HMSAiwYW2JQbdqR6IRaHnvuMoRu4XM9HpXox6m7SwwLkNHN HXU/1aFvv2iTJ0a//u1x9Uo2cnNKTPYZR8ZU47kBIoNpj6SewYL1PtzkJyhIy6yeD9zk9NV4SRNX Sy3WAeDUKN+w/lijCg2mBoGAwY8/z0W37uEm+Xx2ViwWjjOsF3v9SReUVPJYb2Gx7W5+mkYsP1Bd fUnu6hzrQAgGcFn/6moJBg8x/WooCwshRo7ujhvXc0zuC/DGmFK9qrA9itxSCyz8wwcKltdm01Fu LrEXykXJWRqx/ADAMvKfAHAW7+7cfhbpafloDbCxscQvv83D0GGdTeaZwn1keKaf2GDao2QJ3v/L W5VdKx0A7AkLnNFDI5YfAESiq7VurjFtANLsdoiUUqSl5WPCk320upmEIblAw0d2RWFBhca1TPUx yf1xRoFB5Ovfx7e7PHHwisq6TkJCyBAbu9CVVVU3FS0mPwA4OndNEhA6B0CzmUUFBeXw9XNvNTo4 DEMwZGgsnJztcPb0zSb3FDN0TOhRiaf7Go7V33TWGcv3u6t7urOFwKqiXJR8TiPkF4uTal2dY224 fH8AuHQhHRMn92tVxeCdYoLRsVMw/jl2BfX1CqNqu5eTAsvj8mFlYRgv7t8XHfHxZi9QtMh78C8X Jf/SYp//Pixtrb8FDzlzsbgGi7/ZgtaGfv3bY+OW9xAQ4GlU7s4nU4rgaGMYlXl/nHLBexvb8JEi 5EJUdOD0Nhqx/ABQWnqh3s01tgrAWK5jb6XcQUxMMNoaERE0ATc3B4x/sg/y88qMYvI/uZcY0wwg pi+VEyza2garjrg1a/E9vZwR2yWUV5RNCebvCnFyjkYsPwCkZcavAgintCGlFB+8tw41NRK0Ntjb 2+CHZS/i8y+fN2jXz89VjoXjSvXejpt5VpiyJAA7zje/iV73HhHYsftj/LpyHpyd+eT5ECuNuT33 ec2AmQUeoc/Cwgp889VmtFZMmtIfW3d+iMh2/oY3UScUXzxVBFtL/bk71VIBFu/2xNSlAc1umyoQ MHjxpVFYu34B3N0dsX/vBYjF3HlHAqIo0pjbcx9loqRyN5dYIYCBnG/1jVxERbXV26YT+oarqwMm T+kPGxsrXLyQZjCqFzMHi/CknmQHJTKCTWdd8PpaH/ybbgvajH/v4+OKn399BRMn9wPDEFBK8c5b 8XxyyWqUjP3bFRWJja74tmhGERo6z4qhtclcsub3CbB77yfw8HRCa0ZOTgk+eHctEs+n6rUdnQIl +P2VOxDqOF05r8ICm886Y+u/Tqis47a9I0Z1xaefTXuoluLAvot4bd4KHv4J2ZSWvaZJFYIWT6dD A2f1ZBj2DB8Xqm+/aKyKfx0MQ1r1C8CyFJs3ncSPS3aivFz3hSKudkpsWZCDNk66WcyqkTI4fcsO ey454sRNO15RHG9vV7z/0VMNVs+VShZjRn6ErEzu+hGG0D63MhPOatztuY8KcVKeu0uMD0C6ch2b m1sKOztrdO4S2qrJTwhB+w6BmPLUAFBKceN6js5cIYZQLJlegCg/7crMF4qF2HvJET/ud8dn27yw /7IjbpdacsbthUIBZr4wAsuWv4SIyIbzpC2bT2L71jN8evlUalb8p80eoYkHDQ6e7SSE4joAP65j hUIBft+wEF26hsGMu8jPK8PSJTux9+/zWn8JXhhSjvmjyjR6zbp6BukFVkjJt8Ll2zZIum2D/HIL la/To2ckPv7kWYSEejf6e6W4FsOHvNfoZoYNvq6UPJ6RveZvrZMfAMKCZvYnhB7j8zXx8HTCjl0f t3r/v7H5wMpf92HXjnOQyzW/QixgKCb2rIS7gxLOtko42SvgYsvCxV4JJ1sl7K1YSOUE9cqGtBDX CCCuE0BUI0BJlRB5FRYorLBAdokl8kUWLdrtPSLSD/NeG4chQ2ObzQf7+IPf8ddGXmohl9Ky4ruB o/5co853RFDct5RgIZ9jO3cJxfo/31JpZ8eW4sTxqwiP8IO3t6tBvwQFBRVIWH0QO7afQXW16a6R hIX54JVXx2HYiC6c88ALiWmY9sy3vKTxWYrhGdncdbUaZV5gSLfj9VJ2BABfTp+wsAKVlbU6VURz drbDM1O/QUSkH3z93A2WFA4ONug/oAOemzYYfv7uKC4Wo7TEdEpEu3YLx1vvTMKHi55BWLgvZ/Zv XZ0Ms6YvQWUlL53Qk+nZ8R/y+hJqdJJTeIl1c+p0CoRMB0fmJwBcu5oNFxcHdOwUpJNOt7a2hFRa j3ffioejky06dTJsGXILCyGi2wdgylMD8NigTrCzs0ZJscgovwb29jaYMLEvvvpmBmbPGcWL9Pfx 0fu/89WGUjKEmVQmSuIlJaKVmGNEUNxkSrCJ19snYPDLb/Mw8DHdfAFkMjlGDvsA+XllGDe+FxZ9 +hxsba2MhkSUUly5nI0D+y/gxPFrvEJ++v6CjRzdDf0HdICVleqT4A3rj+HTRRv4Hv5LWlb8y3wP 1lrAPTwo7kcQzONzrK2tFf7c9I7O8v/3/H0eb85fCQAICPTCD0tno32HQKN0IYqLRTh3NgXnzqTg woU05OeV6aUdDEMQGNQGMTHBiO0citjOIQgJ9WnRms7FC2l4/tnvoFDwKskttVJYRVzLXSHSO/m7 dJltUS1S/AOgD5/jvbxc8NeWd+Hj66YT6zl75jKcPHHtgXsx/40nEDdrhNEvwFWKa3HzZi5u3shF SkoucnNKkJ9XhrIyzSym2dhYwsfXDT4+bggL90VomA/Cw30RGuoDGw1+QYuKRHhy3Ke8200opqRm x6uURKbVkQ4NneXHsGwiAG8+xwcEeOLPTe/A3UP7IdCysiqMG/3xQ53bu08Uvl4cBy8vF5OLrEgk 9cjLK0NpiRhVlXWoqqpDVXUdqqvqGqwtCAQM7O1t4OhkCwcHGzg42MLDwwne3q46kWysqZFg2jOL ceN6Dj9jBmxNz4qfpOp9tG7mwgJn9CAMOQ7Amtfx4b74Y+NbPNNVW4bTJ69jVtzSh8oOHRxssPCd SZg8pX+rqEE2xJd09sylquQ+FbMM7ZCRkaByXrbWg+wV4sv57s6d00Awkc/LVlFejQuJaRg1prvW dfHbBniirk6K5KT/7yxZX6/AP8eu4Pq12+jeIwL29jZmRhou8VlKmSczsuLV2hhaJytM5eLkm+6u nS0A9Of1KheJcCExDSNGdoWlpYVW29azVztcu3obOY9UBd2+XYxtW07D3cMJke38zV8BLUMqrcdL s39USe6eEHyclrUmQd176mx5tVyU/I+ba2wwAF47PBcWVuDc2RQMH9EF1tbaq4RiGILHBnXC0SPJ EFU8nDMik8lx9HAyLiSmIbZzKJxd7M0s1YZ3UFGNF2f9qFKaNwH9OzUr4GXgODV48gNAYHC3PfVS tisAXlltJcVi/HPsCoYN7wI7O2uttcvKygIDBnbEnt3nIZHUN/g9P68Mf/15HBKJDF27hUMgYMyM 1RBybhdj+nPfIeVmrgpn0RssUz+6omJ5i1JTdUr+wsJLrK9l+50KS8Fg8MgAvW8VTpy4isFDY7Xq fzs62aJjpyDs25PYaGYly1IkXcrAwQOXEBjo1eqK8rUVcIh7/geUFKugFURQoIDFoMzMtS3WiBTo +oGLa6/KPbw67KBKZgwADz7niCpqsH/vBfTsFQkPLYZBff3cEdnOHwf2X2wygUokqsHuneeQcvMO OnQKhJOTnZnFKkKpZPHrir348P11kEpVKKihKGcZweDMzFUZmmiHQB8PX1Z2RdLGqdN2lpAxAHhl mNXWSvH37vNoF9UWAYHa2xw6KKgNQkK8cfhQUrPKa9lZRdi08QSk0np0igmBhYV5x0Y+yM8rw5zZ P2Ln9rOqKttVA2R4etbqy5pqi0BfnVAqvlzj6Nxlq4DQUXy/AHK5Avv2JMLN3Umr6QihYT6IbOeP w4eSmi0uUSpZXLqYjh3bz8DdzRHhEX7mqFAz2LXjHF56cTlybqvssVSyDEakZ8Wf12R7BPrsDLE4 qdbdPnYnBBgLgFdeA6UUx49dQXl5NXr3idLa5DM42BudYkJw6FASFHIl51fp8KEknDp5HYFBbeCr gxQNY0JWZiHemL8S8WsOqiPjWEGAYemZ8YmabpdBmKkI/zgfaoHDAKJUOa9jpyD8+PNcrRan3LiR gzkv/KjSpGzQ4E6YM3cMOsUEt2rS19ZK8cvyv7E24TDf5LRHXHykMxRjU7PjtSJ1YTDf6A5tX3KR CWV7APRW5TwXF3t8v/RF9OkbpbW2FRZWYM4LP+KWihLkMbHBmDZ9KIaP6KLTijV9QyaTY9PGE/jt 130t2aftDMvQ8eqkLRiF2/NflFRelPpatt+ksBTEguc6AHB3ZXDP3+ehkCvRtVs4GEbzbpCDgw3G PdEL+XllSFNBe7OoSISDBy5h+9bTkMsVCA7xNmnV6vp6BTZuOI7X5q3AgX0XUVcnU+s6FDTe0qZm cmrqhmptttfgZmfR0ZMs5RKHtQCeUvXc8Ag/fPvdTK3WBezacQ4fffA7pNJ61S2NgEGPnpF4Ynxv DB+p3ZVrXaKstBI7tp/F+nVHUVzcIsHbegK8nZoVv1QX7TbU0ASJCJ75FgX9StU2WllZYP4b4zE9 bpjWcvNvXM/BwjdXtWjjbWdne4we2x2Pj+uFjp2CjK6OQKlkceb0DWzbehqHDya1XHKFIIUl9NmM jIQknZHMkDs4IihuMggSKGCr6rldu4Xjk8+fQ2ioj9b82iXfb8e6hMO8FAWag7u7IwY81hEDH+uE vv2iDbaskmUprlzJwr49idi3J1FTBTIUFKus6+RvXC1eX6vL5zF4cxMePL0bCLMTFCqzWCgU4PkZ Q/DKq+O0RqiLF9Lw8QfrkZFRoJHrWVoK0a17OHr0jERs51B06BCo0QopVSES1SDx31s4ceIajh+7 qml5xTsMS1+4dTvhoF7cC2P4xN4LhW5RNRJ0H15eLnjn/ckYNbq7VtqnUCixLuEIfl6+G7W1mpUB FAgYREb6I7ZLCNp3CEJwcBsEBnpppaJKKq1HWmo+btzIwc0bObhyOQtpqfna2GNMSikW18qEXxcU rNTbnqdG42gOHLhIWJCb+wGAD6HmvgLde0TgzYUTEROrnfh7cbEIPyzejt27zrXYFeIzZwgM8kRA oBc8PJzh7GwHJyc7ODnZwsnZDg4Otv9xV9gHcif19QqUl1ehuFiMirIqlJZVorSkEoWFFSguEulC M3QPhHgtLS0+S9+cMrq1+IiQmSMopesAqJ1W2btPFBa8PRHR0QFaaWNmRiF+XLYTB/dfMtqdGbWA swA+TsuKP2IwURVj7MV7btAfAB5T9xoMQzByVDfMe22c1jbNuHolGyt+2YPjx65o/UtgwDhNKfNp evbqw4bWMCPOwlrEhIXkvkwovgJg15KXoGevdpg2fQgeG9RJKy3NzS3B+nVHsWXTyUaLZUwQ9QDd Qwh+Ts1MOGaojTT6FMTIwOmBLMOsAjCkpdeKjg7A8zOGYtSYblpJUS4vr8LWzaewbesZ5NwuNkXS 3yQgvysZNl6baQlm8j/yHOEhcS+AYjEAx5ZezMPTCROe7INxT/RuUiu+JaCU4tLFdGzbchr791+E RM00AANhUAFhsZWlbEL67bWXjavpJoQI/zgfKsTHIJgF9XeafAhhYT4YN743JkzsAzc3R423WSqt x7mzKTiw7yKOHb2Mqqo6Y+hqKQj+Zlmy3i/Af//x44sUxvnemiAiguO6UmAZ1FwXaAwWFkL06x+N QYNjMGBgR3h6OWu83QqFEv+eu4VjRy/j/LlbGls40yAuEZD1xFL+x61bv5cbvbtgunOuRUxYSO40 QvEleMol8u40QhAV1fZeSkJHdOiondycsrIqXDifivPnU3HhfCqysgr1EzUi2MIS5bsZGesyTYkh Jl9z19HrOTuJvcVcQrEQPMslVYWLiz1iO4cgJjbkbkpCxyCtpC5L6mS4desOUm7euSdGm4PMjEK1 MkxVmqMAE9Oz4reZGjdaTcFpR6/n7GR2lrMo6DsAtLobtkDAoF1UW8TGhqBL1zBEtQ+Av7+H1jI3 S4rFyMsrw53cUty5U4o7uaUoLhZBJKqBqKIGYnENZDL1tx1lGWWoqVn9VkX++4iOnmuvkErmUkrm a9odag42NpYIDfNFRKQfQkK84efvAT9/d/j4uGpclLe2VgpRRQ1KS8UoK6tCUZEI6an5uHY1G6mp eaqmMKSmZcVHmiIXWq3UQHT0JEtFneM4SuhsaGCNoKWTaVdXB7i5OTyQRBQIGNjZN65SR1n6IFeH UoqqqjrU1EhRVVmLqkYkx1vk8hC8mp4Zv9xMfhNFRHBcV0oxDwRTAFiZe+T/HpUCwvCsrJWVZvKb OEJDZ3gwSmYKAX2aEvRs5f1DQTA6LTN+v6k+oJn8TSA8PC6YKsnTBPRpULRrhdx/Ly0r4StTfkIz +Xl9EWbGCigdAxYjKUF3GJDqhRYgJ6CvpWYlrDD1cTWTX0VERk5zY+UWQ8HSkSAYDsDLhB4vERSz 07Ljr7SGsTSTv4X9Fxr6QpRAqexJCXoBtCdA2kFDeUU6AgvgBAX9IT0rYS+AVlN4YCa/hhEcPNuJ oYoeDKHRIAgGJSEAggEEwjAiSSKAFhDgCgtyipFjd+qd+ILWOFZm8usMi5iQkBxfCyIIYSkNAEvd CeBFCTwAuFNK7QkhDhRwJIANHi7QsQNwP19CCuBekB8iAKAEdQQQE1ARJURMWNz9G6ighC0CJfks oyyUSusK8vK2SMxjcRf/A18b/P13IK5VAAAAAElFTkSuQmCC "
       id="image108997-8-6"
       x="244.53682"
       y="486.94781" />
    <g
       id="g109577-1-2"
       transform="matrix(0.1714347,0,0,0.1714347,241.25306,511.07539)">
      <path
         d="m 79.797436,18.8 c -0.5,-1.6 -1.7,-2.9 -3.2,-3.7 l -30.5,-14.6 c -0.8,-0.4 -1.7,-0.5 -2.5,-0.5 -0.8,0 -1.7,0 -2.5,0.2 l -30.5,14.7 c -1.5000002,0.7 -2.6000002,2 -3.0000002,3.7 L 0.09743584,51.5 c -0.3,1.7 0.1,3.4 1.09999996,4.8 L 22.297436,82.4 c 1.2,1.2 2.9,2 4.6,2.1 h 33.6 c 1.8,0.2 3.5,-0.6 4.6,-2.1 l 21.1,-26.1 c 1,-1.4 1.4,-3.1 1.2,-4.8 z"
         id="path10349-3-1"
         style="fill:#326de6" />
      <path
         d="m 75.097436,50.2 v 0 c -0.1,0 -0.2,0 -0.2,-0.1 0,-0.1 -0.2,-0.1 -0.4,-0.1 -0.4,-0.1 -0.8,-0.1 -1.2,-0.1 -0.2,0 -0.4,0 -0.6,-0.1 h -0.1 c -1.1,-0.1 -2.3,-0.3 -3.4,-0.6 -0.3,-0.1 -0.6,-0.4 -0.7,-0.7 0.1,0 0,0 0,0 v 0 l -0.8,-0.2 c 0.4,-2.9 0.2,-5.9 -0.4,-8.8 -0.7,-2.9 -1.9,-5.7 -3.5,-8.2 l 0.6,-0.6 v 0 -0.1 c 0,-0.3 0.1,-0.7 0.3,-0.9 0.9,-0.8 1.8,-1.4 2.8,-2 v 0 c 0.2,-0.1 0.4,-0.2 0.6,-0.3 0.4,-0.2 0.7,-0.4 1.1,-0.6 0.1,-0.1 0.2,-0.1 0.3,-0.2 0.1,-0.1 0,-0.1 0,-0.2 v 0 c 0.9,-0.7 1.1,-1.9 0.4,-2.8 -0.3,-0.4 -0.9,-0.7 -1.4,-0.7 -0.5,0 -1,0.2 -1.4,0.5 v 0 l -0.1,0.1 c -0.1,0.1 -0.2,0.2 -0.3,0.2 -0.3,0.3 -0.6,0.6 -0.8,0.9 -0.1,0.2 -0.3,0.3 -0.4,0.4 v 0 c -0.7,0.8 -1.6,1.6 -2.5,2.2 -0.2,0.1 -0.4,0.2 -0.6,0.2 -0.1,0 -0.3,0 -0.4,-0.1 h -0.1 l -0.8,0.5 c -0.8,-0.8 -1.7,-1.6 -2.5,-2.4 -3.7,-2.9 -8.3,-4.7 -13,-5.2 l -0.1,-0.8 v 0 0.1 c -0.3,-0.2 -0.4,-0.5 -0.5,-0.8 0,-1.1 0,-2.2 0.2,-3.4 v -0.1 c 0,-0.2 0.1,-0.4 0.1,-0.6 0.1,-0.4 0.1,-0.8 0.2,-1.2 v -0.6 0 c 0.1,-1 -0.7,-2 -1.7,-2.1 -0.6,-0.1 -1.2,0.2 -1.7,0.7 -0.4,0.4 -0.6,0.9 -0.6,1.4 v 0 0.5 c 0,0.4 0.1,0.8 0.2,1.2 0.1,0.2 0.1,0.4 0.1,0.6 v 0.1 c 0.2,1.1 0.2,2.2 0.2,3.4 -0.1,0.3 -0.2,0.6 -0.5,0.8 v 0.2 0 l -0.1,0.8 c -1.1,0.1 -2.2,0.3 -3.4,0.5 -4.7,1 -9,3.5 -12.3,7 l -0.6,-0.4 h -0.1 c -0.1,0 -0.2,0.1 -0.4,0.1 -0.2,0 -0.4,-0.1 -0.6,-0.2 -0.9,-0.7 -1.8,-1.5 -2.5,-2.3 v 0 c -0.1,-0.2 -0.3,-0.3 -0.4,-0.4 -0.3,-0.3 -0.5,-0.6 -0.8,-0.9 -0.1,-0.1 -0.2,-0.1 -0.3,-0.2 -0.1,-0.1 -0.1,-0.1 -0.1,-0.1 v 0 c -0.4,-0.3 -0.9,-0.5 -1.4,-0.5 -0.6,0 -1.1,0.2 -1.4,0.7 -0.6,0.9 -0.4,2.1 0.4,2.8 v 0 c 0.1,0 0.1,0.1 0.1,0.1 0,0 0.2,0.2 0.3,0.2 0.3,0.2 0.7,0.4 1.1,0.6 0.2,0.1 0.4,0.2 0.6,0.3 v 0 c 1,0.6 2,1.2 2.8,2 0.2,0.2 0.4,0.6 0.3,0.9 v -0.1 0 l 0.6,0.6 c -0.1,0.2 -0.2,0.3 -0.3,0.5 -3.1,4.9 -4.4,10.7 -3.5,16.4 l -0.8,0.2 v 0 c 0,0.1 -0.1,0.1 -0.1,0.1 -0.1,0.3 -0.4,0.5 -0.7,0.7 -1.1,0.3 -2.2,0.5 -3.4,0.6 v 0 c -0.2,0 -0.4,0 -0.6,0.1 -0.4,0 -0.8,0.1 -1.2,0.1 -0.1,0 -0.2,0.1 -0.4,0.1 -0.1,0 -0.1,0 -0.2,0.1 v 0 c -1.1,0.2 -1.8,1.2 -1.6,2.3 0,0 0,0 0,0 0.2,0.9 1.1,1.5 2,1.4 0.2,0 0.3,0 0.5,-0.1 v 0 c 0.1,0 0.1,0 0.1,-0.1 0,-0.1 0.3,0 0.4,0 0.4,-0.1 0.8,-0.3 1.1,-0.4 0.2,-0.1 0.4,-0.2 0.6,-0.2 h 0.1 c 1.1,-0.4 2.1,-0.7 3.3,-0.9 h 0.1 c 0.3,0 0.6,0.1 0.8,0.3 0.1,0 0.1,0.1 0.1,0.1 v 0 l 0.9,-0.1 c 1.5,4.6 4.3,8.7 8.2,11.7 0.9,0.7 1.7,1.3 2.7,1.8 l -0.5,0.7 v 0 c 0,0.1 0.1,0.1 0.1,0.1 0.2,0.3 0.2,0.7 0.1,1 -0.4,1 -1,2 -1.6,2.9 v 0.1 c -0.1,0.2 -0.2,0.3 -0.4,0.5 -0.2,0.2 -0.4,0.6 -0.7,1 -0.1,0.1 -0.1,0.2 -0.2,0.3 0,0 0,0.1 -0.1,0.1 v 0 c -0.5,1 -0.1,2.2 0.8,2.7 0.2,0.1 0.5,0.2 0.7,0.2 0.8,0 1.5,-0.5 1.9,-1.2 v 0 c 0,0 0,-0.1 0.1,-0.1 0,-0.1 0.1,-0.2 0.2,-0.3 0.1,-0.4 0.3,-0.7 0.4,-1.1 l 0.2,-0.6 v 0 c 0.3,-1.1 0.8,-2.1 1.3,-3.1 0.2,-0.3 0.5,-0.5 0.8,-0.6 0.1,0 0.1,0 0.1,-0.1 v 0 l 0.4,-0.8 c 2.8,1.1 5.7,1.6 8.7,1.6 1.8,0 3.6,-0.2 5.4,-0.7 1.1,-0.2 2.2,-0.6 3.2,-0.9 l 0.4,0.7 v 0 c 0.1,0 0.1,0 0.1,0.1 0.3,0.1 0.6,0.3 0.8,0.6 0.5,1 1,2 1.3,3.1 v 0.1 l 0.2,0.6 c 0.1,0.4 0.2,0.8 0.4,1.1 0.1,0.1 0.1,0.2 0.2,0.3 0,0 0,0.1 0.1,0.1 v 0 c 0.4,0.7 1.1,1.2 1.9,1.2 0.3,0 0.5,-0.1 0.8,-0.2 0.4,-0.2 0.8,-0.6 0.9,-1.1 0.1,-0.5 0.1,-1 -0.1,-1.5 v 0 c 0,-0.1 -0.1,-0.1 -0.1,-0.1 0,-0.1 -0.1,-0.2 -0.2,-0.3 -0.2,-0.4 -0.4,-0.7 -0.7,-1 -0.1,-0.2 -0.2,-0.3 -0.4,-0.5 v -0.2 c -0.7,-0.9 -1.2,-1.9 -1.6,-2.9 -0.1,-0.3 -0.1,-0.7 0.1,-1 0,-0.1 0.1,-0.1 0.1,-0.1 v 0 l -0.3,-0.8 c 5.1,-3.1 9,-7.9 10.8,-13.6 l 0.8,0.1 v 0 c 0.1,0 0.1,-0.1 0.1,-0.1 0.2,-0.2 0.5,-0.3 0.8,-0.3 h 0.1 c 1.1,0.2 2.2,0.5 3.2,0.9 h 0.1 c 0.2,0.1 0.4,0.2 0.6,0.2 0.4,0.2 0.7,0.4 1.1,0.5 0.1,0 0.2,0.1 0.4,0.1 0.1,0 0.1,0 0.2,0.1 v 0 c 0.2,0.1 0.3,0.1 0.5,0.1 0.9,0 1.7,-0.6 2,-1.4 -0.1,-1.1 -0.9,-1.9 -1.8,-2.1 z m -28.9,-3.1 -2.7,1.3 -2.7,-1.3 -0.7,-2.9 1.9,-2.4 h 3 l 1.9,2.4 z m 16.3,-6.5 c 0.5,2.1 0.6,4.2 0.4,6.3 l -9.5,-2.7 v 0 c -0.9,-0.2 -1.4,-1.1 -1.2,-2 0.1,-0.3 0.2,-0.5 0.4,-0.7 l 7.5,-6.8 c 1.1,1.8 1.9,3.8 2.4,5.9 z m -5.4,-9.6 -8.2,5.8 c -0.7,0.4 -1.7,0.3 -2.2,-0.4 -0.2,-0.2 -0.3,-0.4 -0.3,-0.7 l -0.6,-10.1 c 4.4,0.5 8.3,2.4 11.3,5.4 z m -18.1,-5.1 2,-0.4 -0.5,10 v 0 c 0,0.9 -0.8,1.6 -1.7,1.6 -0.3,0 -0.5,-0.1 -0.8,-0.2 l -8.3,-5.9 c 2.6,-2.5 5.8,-4.3 9.3,-5.1 z m -12.2,8.8 7.4,6.6 v 0 c 0.7,0.6 0.8,1.6 0.2,2.3 -0.2,0.3 -0.4,0.4 -0.8,0.5 l -9.7,2.8 c -0.3,-4.2 0.7,-8.5 2.9,-12.2 z m -1.7,16.9 9.9,-1.7 c 0.8,0 1.6,0.5 1.7,1.3 0.1,0.3 0.1,0.7 -0.1,1 v 0 l -3.8,9.2 c -3.5,-2.3 -6.3,-5.8 -7.7,-9.8 z m 22.7,12.4 c -1.4,0.3 -2.8,0.5 -4.3,0.5 -2.1,0 -4.3,-0.4 -6.3,-1 l 4.9,-8.9 c 0.5,-0.6 1.3,-0.8 2,-0.4 0.3,0.2 0.5,0.4 0.8,0.7 v 0 l 4.8,8.7 c -0.6,0.1 -1.2,0.2 -1.9,0.4 z m 12.2,-8.7 c -1.5,2.4 -3.6,4.5 -6,6 l -3.9,-9.4 c -0.2,-0.8 0.2,-1.6 0.9,-1.9 0.3,-0.1 0.6,-0.2 0.9,-0.2 l 10,1.7 c -0.5,1.4 -1.1,2.7 -1.9,3.8 z"
         id="path10351-1-9"
         style="fill:#ffffff" />
    </g>
    <path
       d="m 244.47046,529.80549 v 11.99168 h -11.99167 v -11.99168 z m -5.99584,2.76732 c -1.07139,0 -1.98432,0.67842 -2.33264,1.62912 v 0 l -0.0962,1.7e-4 c -1.20582,0 -2.18331,0.97749 -2.18331,2.18326 0,1.20579 0.97749,2.18328 2.18331,2.18328 v 0 h 4.85784 c 1.20576,0 2.18326,-0.97749 2.18326,-2.18328 0,-1.20577 -0.9775,-2.18326 -2.18326,-2.18326 v 0 l -0.0963,-1.7e-4 c -0.34831,-0.9507 -1.26124,-1.62912 -2.33264,-1.62912 z"
       fill="#4285f4"
       id="path119683-1-6"
       style="fill-rule:evenodd;stroke:none;stroke-width:0.599584" />
    <text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,4.568093,5.3249002)"
       id="text175834"
       style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect175836);fill:#000000;fill-opacity:1;stroke:none"
       x="715.40039"
       y="0"><tspan
         x="338.57812"
         y="2278.6855"
         id="tspan212495"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212493">Arrows represent &quot;has access to&quot; relationships.
</tspan></tspan><tspan
         x="98.675781"
         y="2328.6855"
         id="tspan212503"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212497">Star ratings represent approximate </tspan><tspan
           style="font-weight:bold;text-align:center;text-anchor:middle"
           id="tspan212499">relative</tspan><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212501"> relationships between the </tspan></tspan><tspan
         x="99.027344"
         y="2378.6855"
         id="tspan212507"><tspan
           style="text-align:center;text-anchor:middle"
           id="tspan212505">diagrams. They are not an indication of absolute performance/security.</tspan></tspan></text>
  </g>
</svg>
