load("//website:defs.bzl", "doc")

package(
    default_applicable_licenses = ["//:license"],
    default_visibility = ["//website:__pkg__"],
    licenses = ["notice"],
)

doc(
    name = "quick_start",
    src = "quick_start.md",
    category = "User Guide",
    permalink = "/docs/user_guide/containerd/quick_start/",
    subcategory = "Containerd",
    weight = "10",
)

doc(
    name = "configuration",
    src = "configuration.md",
    category = "User Guide",
    permalink = "/docs/user_guide/containerd/configuration/",
    subcategory = "Containerd",
    weight = "90",
)

doc(
    name = "containerd_11",
    src = "containerd_11.md",
    category = "User Guide",
    include_in_menu = False,
    permalink = "/docs/user_guide/containerd/containerd_11/",
    subcategory = "Containerd",
)
