# This workflow revives issues that are still referenced in the code, and may
# have been accidentally closed.
name: "Issue reviver"
"on":
  schedule:
    - cron: '0 0 * * *'

jobs:
  issue_reviver:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      if: github.repository == 'google/gvisor'
    - run: make run TARGETS="//tools/github" ARGS="-path=. revive"
      if: github.repository == 'google/gvisor'
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        GITHUB_REPOSITORY: ${{ github.repository }}
