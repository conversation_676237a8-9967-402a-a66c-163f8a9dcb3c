load("//website:defs.bzl", "doc")

package(
    default_applicable_licenses = ["//:license"],
    default_visibility = ["//website:__pkg__"],
    licenses = ["notice"],
)

doc(
    name = "platforms",
    src = "platforms.md",
    category = "Architecture Guide",
    data = [
        "platforms.png",
        "platforms.svg",
    ],
    permalink = "/docs/architecture_guide/platforms/",
    weight = "40",
)

doc(
    name = "resources",
    src = "resources.md",
    category = "Architecture Guide",
    data = [
        "resources.png",
        "resources.svg",
    ],
    permalink = "/docs/architecture_guide/resources/",
    weight = "30",
)

doc(
    name = "intro_to_gvisor",
    src = "intro_to_gvisor.md",
    category = "Architecture Guide",
    data = [
        "isolation_with_gvisor.svg",
        "isolation_with_linux_security_primitives.svg",
        "isolation_with_virtualization.svg",
    ],
    permalink = "/docs/architecture_guide/intro/",
    weight = "10",
)

doc(
    name = "security",
    src = "security.md",
    category = "Architecture Guide",
    data = [
        "security.png",
        "security.svg",
    ],
    permalink = "/docs/architecture_guide/security/",
    weight = "20",
)

doc(
    name = "performance",
    src = "performance.md",
    category = "Architecture Guide",
    permalink = "/docs/architecture_guide/performance/",
    weight = "50",
)

doc(
    name = "networking",
    src = "networking.md",
    category = "Architecture Guide",
    data = [
        "packetflow.svg",
    ],
    permalink = "/docs/architecture_guide/networking/",
    weight = "60",
)
