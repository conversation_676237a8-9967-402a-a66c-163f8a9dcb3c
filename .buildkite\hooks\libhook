#!/bin/bash

# Copyright 2025 The gVisor Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

clear_docker_containers() {
  # Kill any running containers (clear state), except for "bootstrap".
  for id_and_name in $(docker ps --format='{{.ID}}/{{.Names}}'); do
    if [[ "$(echo "$id_and_name" | cut -d'/' -f2-)" == 'bootstrap' ]]; then
      continue
    fi
    timeout --kill-after=10s --preserve-status 30s \
      docker container kill "$(echo "$id_and_name" | cut -d'/' -f1)"
  done
}
