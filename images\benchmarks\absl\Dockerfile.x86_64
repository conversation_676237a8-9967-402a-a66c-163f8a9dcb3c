FROM ubuntu:jammy

RUN set -x \
        && apt-get update \
        && apt-get install -y \
            wget \
            git \
            pkg-config \
            zip \
            g++ \
            zlib1g-dev \
            unzip \
            python3 \
        && rm -rf /var/lib/apt/lists/*

RUN wget https://github.com/bazelbuild/bazel/releases/download/7.3.2/bazel-7.3.2-installer-linux-x86_64.sh
RUN chmod +x bazel-7.3.2-installer-linux-x86_64.sh
RUN ./bazel-7.3.2-installer-linux-x86_64.sh

RUN mkdir abseil-cpp && cd abseil-cpp \
    && git init && git remote add origin https://github.com/abseil/abseil-cpp.git \
    && git fetch --depth 1 origin 79e414672f447ac1b27f8cb27ad61df315a3792b && git checkout FETCH_HEAD
