// Copyright 2018 The gVisor Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package ring0

import (
	"unsafe"
)

// eface mirrors runtime.eface.
type eface struct {
	typ  uintptr
	data unsafe.Pointer
}

// kernelAddr returns the kernel virtual address for the given object.
//
//go:nosplit
func kernelAddr(obj any) uintptr {
	e := (*eface)(unsafe.Pointer(&obj))
	return KernelStartAddress | uintptr(e.data)
}

// kernelFunc returns the address of the given function.
//
//go:nosplit
func kernelFunc(fn uintptr) uintptr {
	return KernelStartAddress | fn
}
