agents:
  queue: release
_templates:
  retry_settings: &retry_settings
    automatic:
      - exit_status: -1
        limit: 10
      - exit_status: "*"
        limit: 2
  common: &common
    timeout_in_minutes: 180
    retry:
      <<: *retry_settings
notify:
  - email: "<EMAIL>"
    if: build.state == "failed"

env:
  # Force a clean checkout every time to avoid reuse of files between runs.
  BUILDKITE_CLEAN_CHECKOUT: true

steps:
  - <<: *common
    label: ":ship: Push all images (x86_64)"
    if: build.branch == "master" && build.tag == null
    commands:
      - make ARCH=x86_64 DOCKER_PUSH_AS_LATEST=true push-all-images
      - make ARCH=x86_64 REMOTE_IMAGE_PREFIX=gcr.io/gvisor-presubmit DOCKER_PUSH_AS_LATEST=true push-all-images
    agents:
      arch: "amd64"
  - <<: *common
    label: ":ship: Push all images (aarch64)"
    if: build.branch == "master" && build.tag == null
    commands:
      - make ARCH=aarch64 DOCKER_PUSH_AS_LATEST=true push-all-images
      - make ARCH=aarch64 REMOTE_IMAGE_PREFIX=gcr.io/gvisor-presubmit DOCKER_PUSH_AS_LATEST=true push-all-images
    agents:
      arch: "arm64"
  - <<: *common
    label: ":slot_machine: Syzkaller smoke test"
    if: build.branch == "master" && build.tag == null
    commands:
      - make syzkaller-smoke-test
    agents:
      arch: "amd64"
  - <<: *common
    label: ":ship: Release"
    if: build.branch == "master" || build.tag != null
    commands:
      - "make BAZEL_OPTIONS='--config=x86_64 --compilation_mode=opt' artifacts/x86_64"
      - "make BAZEL_OPTIONS='--config=aarch64 --compilation_mode=opt' artifacts/aarch64"
      - make release RELEASE_NIGHTLY=$$RELEASE_NIGHTLY
      - cd repo && gsutil cp -r . gs://gvisor/releases/
  - <<: *common
    label: ":ship: Website Deploy"
    if: build.branch == "master" && build.tag == null
    commands:
      # The built website image must be x86_64.
      - make BAZEL_OPTIONS=--config=x86_64 website-deploy
    agents:
      arch: "amd64"
  - <<: *common
    label: ":bun: COS Latest Driver Compatibility Test"
    commands:
      - tools/gpu/cos_drivers_test.sh
  - <<: *common
    label: ":female_supervillain: COS GPU Tests"
    commands:
      - make cos-gpu-all-tests
    agents:
      queue: cos-canary-gpu
  - <<: *common
    label: ":screwdriver: GPU Tests"
    commands:
      - make sudo TARGETS=//tools/gpu:main ARGS="install --latest" || cat /var/log/nvidia-installer.log
      - make gpu-all-tests
    agents:
      queue: gpu
  - label: ":fish: CUDA 12.2 tests"
    # This is its own test rather than being part of the GPU tests,
    # because it takes around 30 minutes to run.
    parallelism: 32
    timeout_in_minutes: 120
    retry:
      <<: *retry_settings
    commands:
      - make sudo TARGETS=//tools/gpu:main ARGS="install --latest" || cat /var/log/nvidia-installer.log
      - make cuda-tests ARGS="--cuda_verify_compatibility=true"
    agents:
      queue: gpu
  - label: ":batfish: CUDA 12.8 tests"
    if: build.env("STAGED_BINARIES") == null && ( build.env("SKIP_GPU_TESTS") == null || build.message =~ /nvidia|nvproxy|gpu/i )
    # This is its own test rather than being part of the GPU tests,
    # because it takes around 30 minutes to run.
    parallelism: 16
    timeout_in_minutes: 60
    retry:
      <<: *retry_settings
    commands:
      - make sudo TARGETS=//tools/gpu:main ARGS="install --latest" || cat /var/log/nvidia-installer.log
      - make cuda-12-8-tests ARGS="--cuda_verify_compatibility=true"
    agents:
      queue: gpu
  - <<: *common
    label: ":screwdriver: All GPU Drivers Test"
    parallelism: 8
    commands:
      - tools/gpu/all_drivers_test.sh
    agents:
      queue: gpu
