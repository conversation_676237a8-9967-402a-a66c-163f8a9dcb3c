# This workflow generates the Go branch. Note that this does not test the Go
# branch, as this is rolled into the main continuous integration pipeline. This
# workflow simply generates and pushes the branch, as long as appropriate
# permissions are available.
name: "Go"
"on":
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
      - "feature/**"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  generate:
    runs-on: ubuntu-latest
    permissions:
      statuses: write
    steps:
    - id: setup
      run: |
        if ! [[ -z "${{ secrets.GO_TOKEN }}" ]]; then
          echo has_token=true >> "$GITHUB_OUTPUT"
        else
          echo has_token=false >> "$GITHUB_OUTPUT"
        fi
    - run: |
        jq -nc '{"state": "pending", "context": "go tests"}' | \
        curl -sL -X POST -d @- \
            -H "Content-Type: application/json" \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "${{ github.event.pull_request.statuses_url }}"
      if: github.event_name == 'pull_request'
    - uses: actions/checkout@v4
      if: github.event_name == 'push' && steps.setup.outputs.has_token == 'true'
      with:
        fetch-depth: 0
        token: '${{ secrets.GO_TOKEN }}'
    - uses: actions/checkout@v4
      if: github.event_name == 'pull_request' || steps.setup.outputs.has_token != 'true'
      with:
        fetch-depth: 0
    - uses: actions/setup-go@v5.5.0
      with:
        go-version-file: 'go.mod'
    - run: tools/go_branch.sh
    - run: git checkout go && git clean -xf . && go build ./...
    - if: github.event_name == 'push'
      run: |
        git remote add upstream "https://github.com/${{ github.repository }}"
        git push upstream go:go
    - if: ${{ success() && github.event_name == 'pull_request' }}
      run: |
        jq -nc '{"state": "success", "context": "go tests"}' | \
        curl -sL  -X POST -d @- \
            -H "Content-Type: application/json" \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "${{ github.event.pull_request.statuses_url }}"
    - if: ${{ failure() && github.event_name == 'pull_request' }}
      run: |
        jq -nc '{"state": "failure", "context": "go tests"}' | \
        curl -sL  -X POST -d @- \
            -H "Content-Type: application/json" \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "${{ github.event.pull_request.statuses_url }}"
