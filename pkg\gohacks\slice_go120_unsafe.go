// Copyright 2023 The gVisor Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//go:build go1.20

package gohacks

import (
	"unsafe"
)

// Slice returns a slice whose underlying array starts at ptr an which length
// and capacity are len.
//
// Slice is a wrapper around unsafe.Slice. Prefer to use unsafe.Slice directly
// if possible.
func Slice[T any](ptr *T, length int) []T {
	return unsafe.Slice(ptr, length)
}
