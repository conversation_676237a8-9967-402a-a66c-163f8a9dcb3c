// Copyright 2018 The gVisor Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Package abi describes the interface between a kernel and userspace.
package abi

import (
	"fmt"
)

// OS describes the target operating system for an ABI.
//
// Note that OS is architecture-independent. The details of the OS ABI will
// vary between architectures.
type OS int

const (
	// Linux is the Linux ABI.
	Linux OS = iota
)

// String implements fmt.Stringer.
func (o OS) String() string {
	switch o {
	case Linux:
		return "linux"
	default:
		return fmt.Sprintf("OS(%d)", o)
	}
}

// ABI is an interface that defines OS-specific interactions.
type ABI interface {
}
