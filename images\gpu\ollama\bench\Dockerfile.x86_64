# https://hub.docker.com/r/ollama/ollama
FROM ollama/ollama:0.5.1

ENV PATH=$PATH:/usr/local/nvidia/bin
ENV OLLAMA_ORIGINS=*
ENV OLLAMA_HOST=0.0.0.0:11434

COPY pull.sh /tmp

# Pre-install models useful for benchmarking.
# These are huge (total ~96 GiB), but necessary to benchmark
# models of various sizes. They are in their own image file to
# keep the test-only image lighter by comparison.

# Useful as embedding model.
RUN /tmp/pull.sh snowflake-arctic-embed2:568m-l-fp16

# Useful as small model.
RUN /tmp/pull.sh gemma2:2b-instruct-fp16

# Useful as mid-size model.
RUN /tmp/pull.sh sailor2:8b-chat-fp16

# Useful as coding-specific model.
RUN /tmp/pull.sh qwen2.5-coder:7b-instruct-q8_0

# Useful as large model.
RUN /tmp/pull.sh llama2:70b-chat-q4_K_S

# Useful as vision model.
RUN /tmp/pull.sh llama3.2-vision:11b-instruct-fp16
