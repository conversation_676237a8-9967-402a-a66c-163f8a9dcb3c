// Copyright 2020 The gVisor Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//go:build go1.25

#include "textflag.h"

#define GOID_OFFSET 152 // +checkoffset runtime g.goid

// func goid() int64
TEXT ·goid(SB),NOSPLIT|NOFRAME,$0-8
  MOVQ (TLS), R14
  MOVQ GOID_OFFSET(R14), R14
  MOVQ R14, ret+0(FP)
  RET
