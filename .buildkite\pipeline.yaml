_templates:
  retry_settings: &retry_settings
    automatic:
      - exit_status: -1
        limit: 10
      - exit_status: "*"
        limit: 2
  common: &common
    timeout_in_minutes: 30
    retry:
      <<: *retry_settings
  source_test: &source_test
    if: build.env("STAGED_BINARIES") == null
  source_test_presubmit: &source_test_presubmit
    if: build.env("STAGED_BINARIES") == null && build.branch != "master"
  source_test_continuous: &source_test_continuous
    if: build.env("STAGED_BINARIES") == null && build.branch == "master"
  platform_specific_agents: &platform_specific_agents {}
  kvm_agents: &kvm_agents {kvm: "true"}
  ubuntu_agents: &ubuntu_agents {os: "ubuntu"}
  docker: &docker
    env:
      BUILDKITE_PIPELINE_INSTALL_RUNTIME: true
  benchmarks: &benchmarks
    timeout_in_minutes: 120
    retry:
      automatic: false
    soft_fail: true
    if: build.branch == "master"
    env:
      # BENCHMARKS_OFFICIAL is set from hooks/pre-command, based
      # on whether this is executing on the master branch.
      BENCHMARKS_DATASET: buildkite
      BENCHMARKS_PROJECT: gvisor-benchmarks
      BENCHMARKS_TABLE: benchmarks
      BENCHMARKS_UPLOAD: true
      BENCHMARKS_PLATFORMS: "kvm systrap"
      BUILDKITE_PIPELINE_INSTALL_RUNTIME: true
    agents:
      <<: *kvm_agents
      <<: *platform_specific_agents
      arch: "amd64"
  netstack_test: &netstack_test
    env:
      PACKAGES: >
        ./pkg/tcpip
        ./pkg/tcpip/adapters/gonet
        ./pkg/tcpip/header
        ./pkg/tcpip/link/channel
        ./pkg/tcpip/network/ipv4
        ./pkg/tcpip/network/ipv6
        ./pkg/tcpip/stack
        ./pkg/tcpip/transport/icmp
        ./pkg/tcpip/transport/tcp
        ./pkg/tcpip/transport/udp
        ./pkg/buffer
        ./pkg/waiter
env:
  # Force a clean checkout every time to avoid reuse of files between runs.
  BUILDKITE_CLEAN_CHECKOUT: true

  # Optional filter for syscall tests.
  SYSCALL_TEST_FILTERS: ''

steps:
  # Run basic smoke tests before preceding to other tests.
  - <<: *common
    <<: *source_test
    label: ":fire: Smoke tests (AMD64)"
    command: make smoke-tests
    agents:
      arch: "amd64"
  - <<: *common
    <<: *source_test
    label: ":fire: Smoke tests (ARM64)"
    command: make smoke-tests
    agents:
      arch: "arm64"
  - <<: *common
    <<: *source_test
    env:
      GLIBC_TUNABLES: glibc.pthread.rseq=0
    label: ":fire: Smoke race tests"
    command: make smoke-race-tests
  - <<: *common
    <<: *source_test
    label: ":speedboat: Compile runsc-plugin-stack (AMD64)"
    command: make runsc-plugin-stack
    agents:
      arch: "amd64"

  # Build runsc and pkg (presubmits only).
  - <<: *common
    <<: *source_test_presubmit
    label: ":world_map: Build runsc and pkg (AMD64)"
    commands:
      - "make build TARGETS=//pkg/..."
      - "make build OPTIONS='--build_tag_filters=-network_plugins' TARGETS='//runsc/...'"
    agents:
      arch: "amd64"

  - <<: *common
    <<: *source_test_presubmit
    label: ":world_map: Build runsc and pkg (ARM64)"
    commands:
      - "make build TARGETS=//pkg/..."
      - "make build OPTIONS='--build_tag_filters=-network_plugins' TARGETS='//runsc/...'"
    agents:
      arch: "arm64"

  # Build everything (continuous only).
  - <<: *common
    <<: *source_test_continuous
    label: ":world_map: Build everything"
    commands:
      - "make build OPTIONS='--build_tag_filters=-network_plugins' TARGETS='//...'"

  # Check that the Go branch builds. This is not technically required, as this build is maintained
  # as a GitHub action in order to preserve this maintaince across forks. However, providing the
  # action here may provide easier debuggability and diagnosis on failure.
  - <<: *common
    <<: *source_test
    label: ":golang: Go branch"
    commands:
      - tools/go_branch.sh
      - git checkout go && git clean -xf .
      - go build ./...

  # Check that commonly used netstack packages build on various platforms.
  - <<: *common
    <<: *source_test
    <<: *netstack_test
    label: ":mac: Netstack on Mac"
    commands:
      - tools/go_branch.sh
      - git checkout go && git clean -xf .
      - GOOS=darwin GOARCH=arm64 go build $$PACKAGES
  - <<: *common
    <<: *source_test
    <<: *netstack_test
    label: ":windows: Netstack on Windows"
    commands:
      - tools/go_branch.sh
      - git checkout go && git clean -xf .
      - GOOS=windows GOARCH=amd64 go build $$PACKAGES
  - <<: *common
    <<: *source_test
    <<: *netstack_test
    label: ":freebsd: Netstack on FreeBSD"
    commands:
      - tools/go_branch.sh
      - git checkout go && git clean -xf .
      - GOOS=freebsd GOARCH=amd64 go build $$PACKAGES
  - <<: *common
    <<: *source_test
    <<: *netstack_test
    label: ":openbsd: Netstack on OpenBSD"
    commands:
      - tools/go_branch.sh
      - git checkout go && git clean -xf .
      - GOOS=openbsd GOARCH=amd64 go build $$PACKAGES
  - <<: *common
    <<: *source_test
    <<: *netstack_test
    label: ":older_man: Netstack on 32-bit Linux"
    commands:
      - tools/go_branch.sh
      - git checkout go && git clean -xf .
      - GOOS=linux GOARCH=mips go build $$PACKAGES

  # GPU workflow.
  - <<: *common
    <<: *source_test
    label: ":firefighter: GPU Smoke Tests"
    commands:
      - make sudo TARGETS=//tools/gpu:main ARGS="install --latest" || cat /var/log/nvidia-installer.log
      - make gpu-smoke-tests
      - make sudo TARGETS=//tools/gpu:main ARGS="validate_checksum"
    agents:
      queue: gpu
  - <<: *common
    <<: *source_test_continuous
    label: ":bun: COS Latest Driver Compatibility Test"
    commands:
      - tools/gpu/cos_drivers_test.sh
  - <<: *common
    label: ":screwdriver: GPU Tests"
    if: build.env("STAGED_BINARIES") == null && ( build.env("SKIP_GPU_TESTS") == null || build.message =~ /nvidia|nvproxy|gpu/i )
    commands:
      - make sudo TARGETS=//tools/gpu:main ARGS="install --latest" || cat /var/log/nvidia-installer.log
      - make gpu-all-tests
    agents:
      queue: gpu
  - <<: *common
    label: ":female_supervillain: COS GPU Tests"
    if: build.env("STAGED_BINARIES") == null && ( build.env("SKIP_GPU_TESTS") == null || build.message =~ /nvidia|nvproxy|gpu/i )
    commands:
      - make cos-gpu-all-tests
    agents:
      queue: cos-canary-gpu
  - label: ":fish: CUDA tests"
    if: build.env("STAGED_BINARIES") == null && ( build.env("SKIP_GPU_TESTS") == null || build.message =~ /nvidia|nvproxy|gpu/i )
    # This is its own test rather than being part of the GPU tests,
    # because it takes around 30 minutes to run.
    parallelism: 8
    timeout_in_minutes: 60
    retry:
      <<: *retry_settings
    commands:
      - make sudo TARGETS=//tools/gpu:main ARGS="install --latest" || cat /var/log/nvidia-installer.log
      - make cuda-tests
    agents:
      queue: gpu
  - <<: *common
    label: ":screwdriver: All GPU Drivers Test"
    if: build.env("STAGED_BINARIES") == null && ( build.env("SKIP_GPU_TESTS") == null || build.message =~ /nvidia|nvproxy|gpu/i )
    parallelism: 8
    commands:
      - tools/gpu/all_drivers_test.sh
    agents:
      queue: gpu

  # Release workflow test.
  - <<: *common
    <<: *source_test
    label: ":ship: Release tests"
    commands:
      - make BAZEL_OPTIONS=--config=x86_64 artifacts/x86_64
      - make BAZEL_OPTIONS=--config=aarch64 artifacts/aarch64
      - make release
    agents:
      arch: "amd64"

  # Images tests.
  - <<: *common
    label: ":docker: Images (x86_64)"
    if: build.env("SKIP_LOADING_IMAGES") == null && build.env("STAGED_BINARIES") == null
    command: make ARCH=x86_64 test-all-test-images
    agents:
      arch: "amd64"
  - <<: *common
    label: ":docker: Images (aarch64)"
    if: build.env("SKIP_LOADING_IMAGES") == null && build.env("STAGED_BINARIES") == null
    command: make ARCH=aarch64 test-all-test-images
    agents:
      arch: "arm64"

  # Basic unit tests.
  - <<: *common
    <<: *source_test
    label: ":golang: Nogo tests"
    command: make nogo-tests
  - <<: *common
    <<: *source_test
    label: ":test_tube: Unit tests (cgroupv1)"
    command: make unit-tests
    agents:
      cgroup: "v1"
      arch: "amd64"
  - <<: *common
    <<: *source_test
    label: ":test_tube: Unit tests (cgroupv2)"
    command: make unit-tests
    agents:
      cgroup: "v2"
      arch: "amd64"
  - <<: *common
    <<: *source_test
    label: ":test_tube: Unit tests (ARM64)"
    command: make unit-tests
    agents:
      arch: "arm64"
  - <<: *common
    <<: *source_test
    <<: *docker
    label: ":test_tube: Container tests (cgroupv1)"
    command: make container-tests
    agents:
      <<: *kvm_agents
      cgroup: "v1"
      arch: "amd64"
  - <<: *common
    <<: *docker
    # This variant is not really a source test, but we annotate it as such to
    # avoid running binary-only tests for all variants of cgroups. It is
    # sufficient to run cgroupv2 variants only for source changes.
    <<: *source_test
    label: ":test_tube: Container tests (cgroupv2)"
    command: make container-tests
    agents:
      <<: *kvm_agents
      cgroup: "v2"
      arch: "amd64"

  # All system call tests.
  - <<: *common
    label: ":toolbox: System call tests (AMD64)"
    command: make BAZEL_OPTIONS=--test_tag_filters=-allsave syscall-tests
    parallelism: 20
    agents:
      <<: *platform_specific_agents
      <<: *kvm_agents
      arch: "amd64"
  - <<: *common
    <<: *source_test
    label: ":muscle: System call tests (ARM64)"
    command: make BAZEL_OPTIONS=--test_tag_filters=-allsave,runsc_ptrace,runsc_systrap syscall-tests
    parallelism: 10
    agents:
      arch: "arm64"
  # With Save Restore on AMD64.
  - <<: *common
    <<: *source_test_continuous
    label: ":muscle: System call save restore tests (AMD64)"
    command: make BAZEL_OPTIONS=--test_tag_filters=save_restore syscall-tests
    parallelism: 20
    agents:
      arch: "amd64"
  # With Save Resume on ARM64.
  - <<: *common
    <<: *source_test_continuous
    label: ":muscle: System call save resume tests (ARM64)"
    command: make BAZEL_OPTIONS=--test_tag_filters=save_resume syscall-tests
    parallelism: 10
    agents:
      arch: "arm64"

  # Integration tests.
  - <<: *common
    <<: *docker
    <<: *source_test
    label: ":docker: Docker tests (cgroupv1)"
    command: make docker-tests
    agents:
      <<: *ubuntu_agents
      arch: "amd64"
      cgroup: "v1"
  - <<: *common
    <<: *docker
    # See above: not truly a source test.
    <<: *source_test
    label: ":docker: Docker tests (cgroupv2)"
    command: make docker-tests
    agents:
      <<: *ubuntu_agents
      arch: "amd64"
      cgroup: "v2"
  - <<: *common
    <<: *docker
    label: ":goggles: Overlay tests"
    command: make overlay-tests
    agents:
      <<: *platform_specific_agents
      <<: *ubuntu_agents
      arch: "amd64"
  - <<: *common
    <<: *source_test
    <<: *docker
    label: ":one-does-not-simply: Port-Forward tests"
    command: make portforward-tests
    agents:
      <<: *ubuntu_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":safety_pin: Host network tests"
    command: make hostnet-tests
    agents:
      <<: *platform_specific_agents
      <<: *ubuntu_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":satellite: gVisor GSO tests"
    command: make swgso-tests
    agents:
      <<: *platform_specific_agents
      <<: *ubuntu_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    <<: *source_test
    label: ":satellite: gVisor network plugin tests"
    command: make plugin-network-tests
    agents:
      <<: *kvm_agents
      arch: "amd64"
  - <<: *common
    <<: *source_test
    label: ":coffee: Do tests"
    command: make do-tests
    agents:
      arch: "amd64"
  - <<: *common
    <<: *docker
    <<: *source_test
    label: ":person_in_lotus_position: KVM tests"
    command: make kvm-tests
    agents:
      <<: *kvm_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    <<: *source_test
    label: ":rocket: Systrap tests"
    command: make systrap-tests
    agents:
      <<: *kvm_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":weight_lifter: Fsstress test"
    command: make fsstress-test
    agents:
      <<: *platform_specific_agents
      <<: *ubuntu_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":docker: Containerd 1.6.2 tests (cgroupv1)"
    command: make containerd-test-1.6.2
    agents:
      <<: *platform_specific_agents
      <<: *ubuntu_agents
      cgroup: "v1"
  - <<: *common
    <<: *docker
    <<: *source_test
    label: ":docker: Containerd 1.6.2 tests (cgroupv2)"
    command: make containerd-test-1.6.2
    agents:
      <<: *ubuntu_agents
      cgroup: "v2"
  - <<: *common
    <<: *source_test
    label: ":podman: Podman"
    commands:
      - sudo RUNTIME_ARGS=--directfs=false ./test/podman/run.sh
      - sudo RUNTIME_ARGS=--directfs=true ./test/podman/run.sh
    agents:
      <<: *ubuntu_agents
      cgroup: "v2"

  # Check the website builds.
  - <<: *common
    <<: *source_test
    label: ":earth_americas: Website tests"
    command: make website-build
    agents:
      arch: "amd64"

  # Networking tests.
  - <<: *common
    <<: *source_test
    label: ":table_tennis_paddle_and_ball: IPTables tests"
    command: make iptables-tests
    agents:
      <<: *ubuntu_agents
      cgroup: "v2"
  - <<: *common
    <<: *source_test
    label: ":construction_worker: Packetdrill tests"
    command: make packetdrill-tests
    agents:
      <<: *ubuntu_agents
  - <<: *common
    <<: *source_test
    label: ":hammer: Packetimpact tests"
    command: make packetimpact-tests
    agents:
      <<: *ubuntu_agents
      # This test is flaky on old agents.
      cgroup: "v2"

  # Runtime tests.
  - <<: *common
    <<: *docker
    label: ":php: PHP runtime tests"
    command: make php8.3.7-runtime-tests RUNTIME_ARGS=--directfs
    parallelism: 2
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":java: Java runtime tests"
    command: make java21-runtime-tests RUNTIME_ARGS=--directfs
    parallelism: 40
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":golang: Go runtime tests"
    command: make go1.22-runtime-tests RUNTIME_ARGS=--directfs
    parallelism: 10
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":node: NodeJS runtime tests"
    command: make nodejs22.2.0-runtime-tests RUNTIME_ARGS=--directfs
    parallelism: 10
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":python: Python runtime tests"
    command: make python3.12.3-runtime-tests RUNTIME_ARGS=--directfs
    parallelism: 2
    agents:
      <<: *platform_specific_agents
      arch: "amd64"

  # Runtime tests (goferfs). Continuous only.
  - <<: *common
    <<: *docker
    label: ":php: PHP runtime tests (goferfs)"
    command: make php8.3.7-runtime-tests RUNTIME_ARGS=--directfs=false
    parallelism: 2
    if: build.branch == "master"
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":java: Java runtime tests (goferfs)"
    command: make java21-runtime-tests RUNTIME_ARGS=--directfs=false
    parallelism: 40
    if: build.branch == "master"
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":golang: Go runtime tests (goferfs)"
    command: make go1.22-runtime-tests RUNTIME_ARGS=--directfs=false
    parallelism: 10
    if: build.branch == "master"
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":node: NodeJS runtime tests (goferfs)"
    command: make nodejs22.2.0-runtime-tests RUNTIME_ARGS=--directfs=false
    parallelism: 10
    if: build.branch == "master"
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *docker
    label: ":python: Python runtime tests (goferfs)"
    command: make python3.12.3-runtime-tests RUNTIME_ARGS=--directfs=false
    parallelism: 2
    if: build.branch == "master"
    agents:
      <<: *platform_specific_agents
      arch: "amd64"
  - <<: *common
    <<: *source_test_continuous
    label: ":slot_machine: Syzkaller smoke test"
    commands:
      - make syzkaller-smoke-test
    agents:
      arch: "amd64"

  # Run basic benchmarks smoke tests (no upload).
  - <<: *common
    <<: *docker
    label: ":fire: Benchmarks smoke test"
    command: make benchmark-platforms BENCHMARKS_TARGETS=test/benchmarks/base:startup_test BENCHMARKS_FILTER=BenchmarkStartupEmpty BENCHMARKS_OPTIONS=-test.benchtime=1ns
    # Use the opposite of the benchmarks filter.
    if: build.branch != "master"
    agents:
      <<: *platform_specific_agents
      <<: *kvm_agents
      arch: "amd64"

  # Run all benchmarks.
  - <<: *benchmarks
    label: ":bazel: ABSL build benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_FILTER="ABSL/page_cache.clean" BENCHMARKS_SUITE=absl BENCHMARKS_TARGETS=test/benchmarks/fs:bazel_test BENCHMARKS_OPTIONS=-test.benchtime=1ns
  - <<: *benchmarks
    label: ":exploding_death_star: gRPC build benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_FILTER="GRPC/page_cache.clean/filesystem.bind" BENCHMARKS_SUITE=grpc-build BENCHMARKS_TARGETS=test/benchmarks/fs:bazel_test BENCHMARKS_OPTIONS=-test.benchtime=1ns
  - <<: *benchmarks
    label: ":metal: FFMPEG benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=ffmpeg BENCHMARKS_TARGETS=test/benchmarks/media:ffmpeg_test BENCHMARKS_OPTIONS=-test.benchtime=1ns
  # For fio, running with --test.benchtime=Xs scales the written/read
  # bytes to several GB. This is not a problem for root/bind/volume mounts,
  # but for tmpfs mounts, the size can grow to more memory than the machine
  # has available. Fix the runs to 1GB written/read for the benchmark.
  - <<: *benchmarks
    label: ":floppy_disk: FIO benchmarks (write :nest_with_eggs:)"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=fio BENCHMARKS_TARGETS=test/benchmarks/fs:fio_test BENCHMARKS_FILTER='BenchmarkFioWrite' BENCHMARKS_OPTIONS=--test.benchtime=1000x
  # For rand(read|write) fio benchmarks, running 15s does not overwhelm the system for tmpfs mounts.
  - <<: *benchmarks
    label: ":cd: FIO benchmarks (read :nest_with_eggs:)"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=fio BENCHMARKS_TARGETS=test/benchmarks/fs:fio_test BENCHMARKS_FILTER='BenchmarkFioRead' BENCHMARKS_OPTIONS=--test.benchtime=1000x
  - <<: *benchmarks
    label: ":floppy_disk: FIO benchmarks (randwrte :empty_nest:)"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=fio BENCHMARKS_TARGETS=test/benchmarks/fs:fio_test BENCHMARKS_FILTER='BenchmarkFioRandWrite' BENCHMARKS_OPTIONS=--test.benchtime=1000x
  - <<: *benchmarks
    label: ":cd: FIO benchmarks (randread :empty_nest:)"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=fio BENCHMARKS_TARGETS=test/benchmarks/fs:fio_test BENCHMARKS_FILTER='BenchmarkFioRandRead' BENCHMARKS_OPTIONS=--test.benchtime=1000x
  - <<: *benchmarks
    label: ":cd: Ruby CI/CD benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=fio BENCHMARKS_TARGETS=test/benchmarks/fs:rubydev_test BENCHMARKS_OPTIONS=-test.benchtime=1ns
  - <<: *benchmarks
    label: ":globe_with_meridians: HTTPD benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_FILTER="Continuous" BENCHMARKS_SUITE=httpd BENCHMARKS_TARGETS=test/benchmarks/network:httpd_test
  - <<: *benchmarks
    label: ":piedpiper: iperf benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=iperf BENCHMARKS_TARGETS=test/benchmarks/network:iperf_test BENCHMARKS_FILTER=BenchmarkIperfOneConnection
  - <<: *benchmarks
    label: ":nginx: nginx benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_FILTER="Continuous" BENCHMARKS_SUITE=nginx BENCHMARKS_TARGETS=test/benchmarks/network:nginx_test
  - <<: *benchmarks
    label: ":node: node benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=node BENCHMARKS_TARGETS=test/benchmarks/network:node_test
  - <<: *benchmarks
    label: ":redis: Redis benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=redis BENCHMARKS_TARGETS=test/benchmarks/database:redis_test BENCHMARKS_FILTER=BenchmarkRedis/operation
  - <<: *benchmarks
    label: ":ruby: Ruby benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=ruby BENCHMARKS_TARGETS=test/benchmarks/network:ruby_test
  - <<: *benchmarks
    label: ":gorilla: Usage benchmarks"
    command: make -i benchmark-platforms  BENCHMARKS_SUITE=usage BENCHMARKS_TARGETS=test/benchmarks/base:usage_test
  - <<: *benchmarks
    label: ":speedboat: Startup benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=startup BENCHMARKS_TARGETS=test/benchmarks/base:startup_test
  - <<: *benchmarks
    label: ":computer: sysbench benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=sysbench BENCHMARKS_TARGETS=test/benchmarks/base:sysbench_test
  - <<: *benchmarks
    label: ":tensorflow: TensorFlow benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=tensorflow BENCHMARKS_TARGETS=test/benchmarks/ml:tensorflow_test BENCHMARKS_FILTER=BenchmarkTensorflowDashboard
  - <<: *benchmarks
    label: ":gear: Syscall benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=syscall BENCHMARKS_TARGETS=test/benchmarks/base:syscallbench_test
  - <<: *benchmarks
    label: ":thread: hackbench benchmarks"
    command: make -i benchmark-platforms BENCHMARKS_SUITE=hackbench BENCHMARKS_TARGETS=test/benchmarks/base:hackbench_test
