FROM ubuntu:jammy
R<PERSON> apt-get update && apt-get install -y \
  curl \
  dumb-init \
  g++ \
  make \
  python3.10

WORKDIR /root
ARG VERSION=v22.2.0
RUN curl -o node-${VERSION}.tar.gz https://nodejs.org/dist/${VERSION}/node-${VERSION}.tar.gz \
 && tar -zxf node-${VERSION}.tar.gz \
 && rm -f node-${VERSION}.tar.gz

WORKDIR /root/node-${VERSION}
RUN ./configure
RUN make -j $(nproc) test-build

# Including dumb-init emulates the Linux "init" process, preventing the failure
# of tests involving worker processes.
ENTRYPOINT ["/usr/bin/dumb-init"]
