// Copyright 2018 The gVisor Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package gvisor;

// DebugEvent encapsulates any other event protobuf in text format. This is
// useful because clients reading events emitted this way do not need to link
// the event protobufs to display them in a human-readable format.
message DebugEvent {
  // Name of the inner message.
  string name = 1;
  // Text representation of the inner message content.
  string text = 2;
}
