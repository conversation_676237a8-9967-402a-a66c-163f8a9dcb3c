load("//website:defs.bzl", "doc")

package(
    default_applicable_licenses = ["//:license"],
    default_visibility = ["//website:__pkg__"],
    licenses = ["notice"],
)

doc(
    name = "docker",
    src = "docker.md",
    category = "User Guide",
    permalink = "/docs/user_guide/quick_start/docker/",
    subcategory = "Quick Start",
    weight = "11",
)

doc(
    name = "kubernetes",
    src = "kubernetes.md",
    category = "User Guide",
    permalink = "/docs/user_guide/quick_start/kubernetes/",
    subcategory = "Quick Start",
    weight = "12",
)

doc(
    name = "oci",
    src = "oci.md",
    category = "User Guide",
    permalink = "/docs/user_guide/quick_start/oci/",
    subcategory = "Quick Start",
    weight = "13",
)
