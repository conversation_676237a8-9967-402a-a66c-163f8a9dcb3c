#!/bin/bash

# Copyright 2020 The gVisor Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -xeuo pipefail -m

cd initramfs
find . | cpio -v -o -c -R root:root | gzip -9 >> ../initramfs-lts
cd ..

qemu-system-aarch64  -M virt -m 512M -cpu cortex-a57 \
  -kernel vmlinuz-lts -initrd initramfs-lts \
  -append "console=ttyAMA0 panic=-1" -nographic -no-reboot \
  | tee /dev/stderr | grep "runsc exited with code 0"

echo "PASS"
