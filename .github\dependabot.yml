# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    # Look for GitHub Actions workflows in the `root` directory
    directory: "/"
    # Check the for updates once a week
    schedule:
      interval: "weekly"
