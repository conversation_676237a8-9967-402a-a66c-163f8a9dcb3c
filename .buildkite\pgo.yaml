agents:
  queue: release
_templates:
  common: &common
    timeout_in_minutes: 600
    retry:
      automatic:
        - exit_status: -1
          limit: 10
        - exit_status: "*"
          limit: 2

notify:
  - email: "<EMAIL>"  # TODO(b/323401901): Make this not go to /dev/null
    if: build.state == "failed"

env:
  # Force a clean checkout every time to avoid reuse of files between runs.
  BUILDKITE_CLEAN_CHECKOUT: true

steps:
  - <<: *common
    label: ":arrows_counterclockwise: Refresh PGO profiles (x86_64)"
    if: build.branch == "master" && build.tag == null
    commands:
      - ./.buildkite/scripts/pgo/maybe-skip.sh --tolerate-no-profile-changes make ARCH=x86_64 benchmark-refresh-pgo BENCHMARKS_PLATFORMS=systrap
      - ./.buildkite/scripts/pgo/maybe-skip.sh --tolerate-no-profile-changes make ARCH=x86_64 benchmark-refresh-pgo BENCHMARKS_PLATFORMS=kvm
      - ./.buildkite/scripts/pgo/maybe-skip.sh ./.buildkite/scripts/pgo/commit-update.sh
    agents:
      arch: "amd64"
