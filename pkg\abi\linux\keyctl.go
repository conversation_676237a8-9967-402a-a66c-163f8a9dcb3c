// Copyright 2023 The gVisor Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package linux

// Constants used by keyctl(2) and other keyrings-related syscalls.
// Source: include/uapi/linux/keyctl.h

const (
	KEY_SPEC_SESSION_KEYRING = -3
)

const (
	KEYCTL_GET_KEYRING_ID       = 0
	KEYCTL_JOIN_SESSION_KEYRING = 1
	KEYCTL_SETPERM              = 5
	KEYCTL_DESCRIBE             = 6
)
