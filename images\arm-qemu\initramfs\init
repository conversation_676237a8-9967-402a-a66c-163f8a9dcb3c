#!/bin/sh

# Copyright 2020 The gVisor Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This script is started as the init process in a test virtual machine,
# it does all required initialization steps and run a test command inside a
# gVisor instance.

set -x -e

/bin/busybox mkdir -p /usr/bin /usr/sbin /proc /sys /dev /tmp

/bin/busybox --install -s
export PATH=/usr/bin:/bin:/usr/sbin:/sbin

mount -t proc -o noexec,nosuid,nodev proc /proc
mount -t sysfs -o noexec,nosuid,nodev sysfs /sys
mount -t devtmpfs -o exec,nosuid,mode=0755,size=2M devtmpfs /dev

uname -a
/runsc --TESTONLY-unsafe-nonroot --rootless --network none --debug --alsologtostderr do uname -a
echo "runsc exited with code $?"

# Shutdown the VM. poweroff and halt doesn't work for unknown reasons.
# qemu is started with the -no-reboot flag, so the VM will be terminated.
reboot -f
exit 1
